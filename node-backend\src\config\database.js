/**
 * 数据库配置和连接管理
 */

const { Pool } = require('pg');
const redis = require('redis');

class DatabaseManager {
  constructor() {
    this.pgPool = null;
    this.redisClient = null;
  }

  /**
   * 初始化PostgreSQL连接池
   */
  async initPostgreSQL() {
    try {
      this.pgPool = new Pool({
        host: process.env.DB_HOST || 'localhost',
        port: process.env.DB_PORT || 5432,
        database: process.env.DB_NAME || 'plantapp_dev',
        user: process.env.DB_USER || 'plantapp_user',
        password: process.env.DB_PASSWORD || '',
        ssl: process.env.DB_SSL === 'true' ? { rejectUnauthorized: false } : false,
        max: 20,
        idleTimeoutMillis: 30000,
        connectionTimeoutMillis: 2000,
      });

      // 测试连接
      const client = await this.pgPool.connect();
      await client.query('SELECT NOW()');
      client.release();
      
      console.log('✅ PostgreSQL连接成功');
      return true;
    } catch (error) {
      console.error('❌ PostgreSQL连接失败:', error.message);
      return false;
    }
  }

  /**
   * 初始化Redis连接
   */
  async initRedis() {
    // Check if Redis is enabled
    if (process.env.REDIS_ENABLED === 'false') {
      console.log('⚠️ Redis连接已禁用');
      return false;
    }

    try {
      this.redisClient = redis.createClient({
        host: process.env.REDIS_HOST || 'localhost',
        port: process.env.REDIS_PORT || 6379,
        password: process.env.REDIS_PASSWORD || undefined,
        db: process.env.REDIS_DB || 0,
        retryDelayOnFailover: 100,
        maxRetriesPerRequest: 3,
      });

      this.redisClient.on('error', (err) => {
        console.error('Redis连接错误:', err);
      });

      await this.redisClient.connect();
      await this.redisClient.ping();
      
      console.log('✅ Redis连接成功');
      return true;
    } catch (error) {
      console.error('❌ Redis连接失败:', error.message);
      return false;
    }
  }

  /**
   * 检查数据库连接状态
   */
  async checkHealth() {
    const health = {
      database: 'disconnected',
      redis: 'disconnected'
    };

    // 检查PostgreSQL
    try {
      if (this.pgPool) {
        const client = await this.pgPool.connect();
        await client.query('SELECT 1');
        client.release();
        health.database = 'connected';
      }
    } catch (error) {
      health.database = 'error';
    }

    // 检查Redis
    try {
      if (this.redisClient && this.redisClient.isOpen) {
        await this.redisClient.ping();
        health.redis = 'connected';
      }
    } catch (error) {
      health.redis = 'error';
    }

    return health;
  }

  /**
   * 获取PostgreSQL连接池
   */
  getPool() {
    return this.pgPool;
  }

  /**
   * 获取Redis客户端
   */
  getRedisClient() {
    return this.redisClient;
  }

  /**
   * 关闭所有连接
   */
  async close() {
    try {
      if (this.pgPool) {
        await this.pgPool.end();
        console.log('PostgreSQL连接池已关闭');
      }
      
      if (this.redisClient) {
        await this.redisClient.quit();
        console.log('Redis连接已关闭');
      }
    } catch (error) {
      console.error('关闭数据库连接时出错:', error);
    }
  }
}

// 创建单例实例
const databaseManager = new DatabaseManager();

module.exports = databaseManager;
