/**
 * 护理记录控制器
 * 处理护理记录相关的API请求
 */

const CareRecord = require('../models/CareRecord');
const Plant = require('../models/Plant');
const { logger } = require('../middlewares/errorHandler');

class CareRecordController {
  /**
   * 创建护理记录
   * POST /api/v1/care-records
   */
  static async createCareRecord(req, res) {
    try {
      const userId = req.user.id;
      const careData = req.body;

      const newCareRecord = await CareRecord.create(careData, userId);

      logger.info(`用户 ${userId} 创建了新护理记录: ${newCareRecord.type} for plant ${newCareRecord.plantId}`);

      res.status(201).json({
        success: true,
        message: '护理记录创建成功',
        data: {
          careRecord: newCareRecord.toJSON()
        }
      });
    } catch (error) {
      logger.error('创建护理记录失败:', error);
      
      if (error.message.includes('植物不存在') || error.message.includes('无效的护理类型')) {
        return res.status(400).json({
          error: {
            message: error.message,
            status: 400
          }
        });
      }
      
      res.status(500).json({
        error: {
          message: '创建护理记录失败，请稍后重试',
          status: 500
        }
      });
    }
  }

  /**
   * 获取护理记录列表
   * GET /api/v1/care-records
   */
  static async getCareRecords(req, res) {
    try {
      const userId = req.user.id;
      const options = {
        page: parseInt(req.query.page) || 1,
        limit: parseInt(req.query.limit) || 10,
        plantId: req.query.plantId ? parseInt(req.query.plantId) : undefined,
        type: req.query.type,
        startDate: req.query.startDate,
        endDate: req.query.endDate
      };

      // 验证分页参数
      if (options.page < 1) options.page = 1;
      if (options.limit < 1 || options.limit > 100) options.limit = 10;

      // 验证日期格式
      if (options.startDate && isNaN(Date.parse(options.startDate))) {
        return res.status(400).json({
          error: {
            message: '开始日期格式不正确',
            status: 400
          }
        });
      }

      if (options.endDate && isNaN(Date.parse(options.endDate))) {
        return res.status(400).json({
          error: {
            message: '结束日期格式不正确',
            status: 400
          }
        });
      }

      // 验证护理类型
      if (options.type && !CareRecord.isValidCareType(options.type)) {
        return res.status(400).json({
          error: {
            message: '无效的护理类型',
            status: 400
          }
        });
      }

      const result = await CareRecord.findByUserId(userId, options);

      res.json({
        success: true,
        message: '获取护理记录列表成功',
        data: {
          careRecords: result.careRecords.map(record => record.toJSON()),
          pagination: {
            page: result.page,
            limit: result.limit,
            total: result.total,
            totalPages: result.totalPages
          }
        }
      });
    } catch (error) {
      logger.error('获取护理记录列表失败:', error);
      
      res.status(500).json({
        error: {
          message: '获取护理记录列表失败，请稍后重试',
          status: 500
        }
      });
    }
  }

  /**
   * 获取单个护理记录详情
   * GET /api/v1/care-records/:id
   */
  static async getCareRecordById(req, res) {
    try {
      const userId = req.user.id;
      const recordId = req.params.id;

      // 验证记录ID
      if (!recordId || isNaN(recordId)) {
        return res.status(400).json({
          error: {
            message: '无效的护理记录ID',
            status: 400
          }
        });
      }

      const careRecord = await CareRecord.findByIdAndUserId(recordId, userId);

      if (!careRecord) {
        return res.status(404).json({
          error: {
            message: '护理记录不存在或不属于当前用户',
            status: 404
          }
        });
      }

      res.json({
        success: true,
        message: '获取护理记录详情成功',
        data: {
          careRecord: careRecord.toJSON()
        }
      });
    } catch (error) {
      logger.error('获取护理记录详情失败:', error);
      
      res.status(500).json({
        error: {
          message: '获取护理记录详情失败，请稍后重试',
          status: 500
        }
      });
    }
  }

  /**
   * 更新护理记录
   * PUT /api/v1/care-records/:id
   */
  static async updateCareRecord(req, res) {
    try {
      const userId = req.user.id;
      const recordId = req.params.id;
      const updateData = req.body;

      // 验证记录ID
      if (!recordId || isNaN(recordId)) {
        return res.status(400).json({
          error: {
            message: '无效的护理记录ID',
            status: 400
          }
        });
      }

      // 检查护理记录是否存在
      const existingRecord = await CareRecord.findByIdAndUserId(recordId, userId);
      if (!existingRecord) {
        return res.status(404).json({
          error: {
            message: '护理记录不存在或不属于当前用户',
            status: 404
          }
        });
      }

      const updatedRecord = await CareRecord.updateByIdAndUserId(recordId, userId, updateData);

      if (!updatedRecord) {
        return res.status(404).json({
          error: {
            message: '更新失败，护理记录不存在',
            status: 404
          }
        });
      }

      logger.info(`用户 ${userId} 更新了护理记录 ${recordId}`);

      res.json({
        success: true,
        message: '护理记录更新成功',
        data: {
          careRecord: updatedRecord.toJSON()
        }
      });
    } catch (error) {
      logger.error('更新护理记录失败:', error);
      
      if (error.message.includes('无效的护理类型')) {
        return res.status(400).json({
          error: {
            message: error.message,
            status: 400
          }
        });
      }
      
      res.status(500).json({
        error: {
          message: '更新护理记录失败，请稍后重试',
          status: 500
        }
      });
    }
  }

  /**
   * 删除护理记录
   * DELETE /api/v1/care-records/:id
   */
  static async deleteCareRecord(req, res) {
    try {
      const userId = req.user.id;
      const recordId = req.params.id;

      // 验证记录ID
      if (!recordId || isNaN(recordId)) {
        return res.status(400).json({
          error: {
            message: '无效的护理记录ID',
            status: 400
          }
        });
      }

      // 检查护理记录是否存在
      const existingRecord = await CareRecord.findByIdAndUserId(recordId, userId);
      if (!existingRecord) {
        return res.status(404).json({
          error: {
            message: '护理记录不存在或不属于当前用户',
            status: 404
          }
        });
      }

      const deleted = await CareRecord.deleteByIdAndUserId(recordId, userId);

      if (!deleted) {
        return res.status(404).json({
          error: {
            message: '删除失败，护理记录不存在',
            status: 404
          }
        });
      }

      logger.info(`用户 ${userId} 删除了护理记录 ${recordId}`);

      res.json({
        success: true,
        message: '护理记录删除成功'
      });
    } catch (error) {
      logger.error('删除护理记录失败:', error);
      
      res.status(500).json({
        error: {
          message: '删除护理记录失败，请稍后重试',
          status: 500
        }
      });
    }
  }

  /**
   * 获取护理记录统计信息
   * GET /api/v1/care-records/stats
   */
  static async getCareRecordStats(req, res) {
    try {
      const userId = req.user.id;
      const plantId = req.query.plantId ? parseInt(req.query.plantId) : undefined;

      // 获取护理记录
      const result = await CareRecord.findByUserId(userId, { 
        limit: 1000,
        plantId: plantId
      });
      const records = result.careRecords;

      // 计算统计信息
      const stats = {
        totalRecords: records.length,
        typeDistribution: {},
        recentRecords: 0,
        averagePerWeek: 0
      };

      // 统计护理类型分布
      CareRecord.CARE_TYPES.forEach(type => {
        stats.typeDistribution[type] = records.filter(r => r.type === type).length;
      });

      // 统计最近一周的记录
      const weekAgo = new Date();
      weekAgo.setDate(weekAgo.getDate() - 7);
      stats.recentRecords = records.filter(r => {
        const careDate = new Date(r.careDate);
        return careDate > weekAgo;
      }).length;

      // 计算平均每周护理次数（基于最近30天）
      const monthAgo = new Date();
      monthAgo.setDate(monthAgo.getDate() - 30);
      const recentMonthRecords = records.filter(r => {
        const careDate = new Date(r.careDate);
        return careDate > monthAgo;
      }).length;
      stats.averagePerWeek = Math.round((recentMonthRecords / 4) * 10) / 10;

      res.json({
        success: true,
        message: '获取护理记录统计成功',
        data: {
          stats
        }
      });
    } catch (error) {
      logger.error('获取护理记录统计失败:', error);
      
      res.status(500).json({
        error: {
          message: '获取护理记录统计失败，请稍后重试',
          status: 500
        }
      });
    }
  }
}

module.exports = CareRecordController;
