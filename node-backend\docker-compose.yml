version: '3.8'

services:
  plantapp-backend:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: plantapp-backend-all-in-one
    ports:
      - "3000:3000"  # Node.js backend
      - "5432:5432"  # PostgreSQL
      - "6379:6379"  # Redis
    environment:
      - NODE_ENV=production
      - PORT=3000
      - DB_HOST=localhost
      - DB_PORT=5432
      - DB_NAME=plantapp_dev
      - DB_USER=plantapp_user
      - DB_PASSWORD=secure_password
      - REDIS_HOST=localhost
      - REDIS_PORT=6379
      - JWT_SECRET=your-super-secure-jwt-secret-key-change-in-production-12345
      - OPENROUTER_API_KEY=your-openrouter-api-key-here
    volumes:
      - plantapp_postgres_data:/var/lib/postgresql/data
      - plantapp_redis_data:/var/lib/redis
      - plantapp_logs:/app/logs
      - plantapp_uploads:/app/uploads
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s

volumes:
  plantapp_postgres_data:
    driver: local
  plantapp_redis_data:
    driver: local
  plantapp_logs:
    driver: local
  plantapp_uploads:
    driver: local

networks:
  default:
    name: plantapp-network
