/**
 * 植物识别与护理应用 - 演示服务器
 * 不依赖数据库的简化版本，用于演示API功能
 */

const express = require('express');
const cors = require('cors');
const helmet = require('helmet');
const morgan = require('morgan');
const compression = require('compression');
const multer = require('multer');

const app = express();
const PORT = process.env.PORT || 3000;

// 中间件配置
app.use(helmet());
app.use(cors());
app.use(compression());
app.use(morgan('combined'));
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true, limit: '10mb' }));

// 文件上传配置
const upload = multer({
  limits: {
    fileSize: 10 * 1024 * 1024, // 10MB
  },
  fileFilter: (req, file, cb) => {
    if (file.mimetype.startsWith('image/')) {
      cb(null, true);
    } else {
      cb(new Error('只允许上传图片文件'), false);
    }
  }
});

// 模拟数据
const mockPlants = [
  {
    id: '1',
    name: '<PERSON>a deliciosa',
    commonName: '龟背竹',
    scientificName: 'Monstera deliciosa',
    family: '天南星科',
    description: '大型观叶植物，叶片有特征性的孔洞',
    careLevel: 'easy',
    lightRequirement: 'bright-indirect',
    waterFrequency: 'weekly',
    humidity: 'medium-high'
  },
  {
    id: '2',
    name: 'Ficus lyrata',
    commonName: '琴叶榕',
    scientificName: 'Ficus lyrata',
    family: '桑科',
    description: '大型观叶植物，叶片形似小提琴',
    careLevel: 'medium',
    lightRequirement: 'bright-indirect',
    waterFrequency: 'weekly',
    humidity: 'medium'
  }
];

// 健康检查端点
app.get('/health', (req, res) => {
  res.json({
    status: 'healthy',
    timestamp: new Date().toISOString(),
    version: '1.0.0',
    services: {
      api: 'running',
      database: 'demo-mode',
      ai: 'mock-enabled'
    }
  });
});

// API健康检查
app.get('/api/v1/health', (req, res) => {
  res.json({
    status: 'healthy',
    message: '植物识别与护理API演示版本',
    timestamp: new Date().toISOString(),
    features: {
      plantIdentification: 'available',
      careAdvice: 'available',
      userAuth: 'demo-mode',
      database: 'mock-data'
    }
  });
});

// 植物识别API
app.post('/api/v1/identification/identify', upload.single('image'), (req, res) => {
  try {
    const { textDescription, molecularData, options } = req.body;
    const hasImage = !!req.file;
    const hasText = !!textDescription;
    const hasMolecular = !!molecularData;

    // 模拟AI识别结果
    const mockResult = {
      success: true,
      identification: {
        scientificName: 'Monstera deliciosa',
        commonName: '龟背竹',
        family: '天南星科',
        genus: 'Monstera',
        species: 'deliciosa',
        confidence: hasImage && hasText ? 0.95 : hasImage ? 0.85 : 0.75,
        characteristics: [
          '大型观叶植物',
          '叶片有特征性的孔洞和裂缝',
          '适合室内种植',
          '喜欢明亮的散射光'
        ]
      },
      inputAnalysis: {
        hasImage,
        hasText,
        hasMolecular,
        imageQuality: hasImage ? 'good' : null,
        textRelevance: hasText ? 'high' : null,
        molecularMatch: hasMolecular ? 'partial' : null
      },
      careRecommendations: {
        light: '明亮的散射光，避免直射阳光',
        water: '土壤表面干燥时浇水，约每周一次',
        humidity: '保持中等到高湿度（50-60%）',
        temperature: '18-24°C最适宜',
        fertilizer: '生长季节每月施肥一次'
      },
      timestamp: new Date().toISOString(),
      processingTime: Math.random() * 2000 + 500 // 模拟处理时间
    };

    // 模拟处理延迟
    setTimeout(() => {
      res.json(mockResult);
    }, 1000);

  } catch (error) {
    res.status(500).json({
      success: false,
      error: {
        message: '植物识别失败',
        details: error.message
      }
    });
  }
});

// 护理建议API
app.post('/api/v1/identification/care-advice', (req, res) => {
  try {
    const { plantName, conditions = {} } = req.body;

    if (!plantName) {
      return res.status(400).json({
        success: false,
        error: {
          message: '请提供植物名称'
        }
      });
    }

    // 模拟护理建议
    const mockAdvice = {
      success: true,
      plantName,
      advice: {
        watering: {
          frequency: '每周1-2次',
          amount: '浇透但不积水',
          timing: '早晨或傍晚',
          seasonalAdjustment: conditions.season === 'winter' ? '减少浇水频率' : '保持正常频率'
        },
        lighting: {
          requirement: '明亮的散射光',
          duration: '每天6-8小时',
          placement: conditions.location === 'indoor' ? '靠近窗户但避免直射' : '半阴环境'
        },
        temperature: {
          optimal: '18-24°C',
          minimum: '15°C',
          maximum: '28°C',
          notes: conditions.climate === 'tropical' ? '注意通风' : '注意保温'
        },
        humidity: {
          level: '50-60%',
          methods: ['使用加湿器', '水盘增湿', '叶面喷雾'],
          monitoring: '使用湿度计监测'
        },
        fertilizing: {
          frequency: '生长季节每月一次',
          type: '平衡型液体肥料',
          dilution: '按说明书稀释至1/2浓度',
          winterCare: '冬季停止施肥'
        },
        pruning: {
          timing: '春季或夏季',
          method: '剪除枯黄叶片和过长枝条',
          tools: '使用消毒的剪刀',
          afterCare: '伤口处涂抹杀菌剂'
        }
      },
      conditions,
      timestamp: new Date().toISOString()
    };

    res.json(mockAdvice);

  } catch (error) {
    res.status(500).json({
      success: false,
      error: {
        message: '护理建议生成失败',
        details: error.message
      }
    });
  }
});

// 服务状态检查
app.get('/api/v1/identification/service-status', (req, res) => {
  res.json({
    success: true,
    status: 'healthy',
    services: {
      openRouter: {
        status: 'demo-mode',
        message: '演示模式，使用模拟数据'
      },
      imageProcessing: {
        status: 'available',
        supportedFormats: ['jpeg', 'png', 'webp']
      },
      textAnalysis: {
        status: 'available',
        languages: ['zh-CN', 'en']
      },
      molecularAnalysis: {
        status: 'available',
        formats: ['FASTA', 'raw-sequence']
      }
    },
    limits: {
      maxFileSize: '10MB',
      maxRequestsPerMinute: 100,
      maxBatchSize: 10
    },
    timestamp: new Date().toISOString()
  });
});

// 植物数据库API（演示）
app.get('/api/v1/plants', (req, res) => {
  const { search, limit = 10 } = req.query;
  let results = mockPlants;

  if (search) {
    results = mockPlants.filter(plant => 
      plant.name.toLowerCase().includes(search.toLowerCase()) ||
      plant.commonName.includes(search) ||
      plant.scientificName.toLowerCase().includes(search.toLowerCase())
    );
  }

  res.json({
    success: true,
    plants: results.slice(0, parseInt(limit)),
    total: results.length,
    timestamp: new Date().toISOString()
  });
});

// 错误处理中间件
app.use((error, req, res, next) => {
  console.error('Error:', error);
  
  if (error instanceof multer.MulterError) {
    if (error.code === 'LIMIT_FILE_SIZE') {
      return res.status(400).json({
        success: false,
        error: {
          message: '文件太大，最大支持10MB'
        }
      });
    }
  }

  res.status(500).json({
    success: false,
    error: {
      message: '服务器内部错误',
      details: process.env.NODE_ENV === 'development' ? error.message : undefined
    }
  });
});

// 404处理
app.use('*', (req, res) => {
  res.status(404).json({
    success: false,
    error: {
      message: `路径 ${req.originalUrl} 未找到`,
      availableEndpoints: [
        'GET /health',
        'GET /api/v1/health',
        'POST /api/v1/identification/identify',
        'POST /api/v1/identification/care-advice',
        'GET /api/v1/identification/service-status',
        'GET /api/v1/plants'
      ]
    }
  });
});

// 启动服务器
app.listen(PORT, () => {
  console.log(`
🌱 植物识别与护理应用 - 演示服务器
🚀 服务器运行在: http://localhost:${PORT}
📚 API文档: http://localhost:${PORT}/api/v1/health
🔍 健康检查: http://localhost:${PORT}/health

可用的API端点:
• POST /api/v1/identification/identify - 植物识别
• POST /api/v1/identification/care-advice - 护理建议
• GET /api/v1/identification/service-status - 服务状态
• GET /api/v1/plants - 植物数据库

演示模式说明:
- 不需要数据库连接
- 使用模拟AI识别结果
- 支持文件上传测试
- 所有功能都可以正常演示
  `);
});

module.exports = app;
