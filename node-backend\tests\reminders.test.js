/**
 * 提醒服务CRUD API测试
 * 测试提醒数据的增删改查功能
 */

const request = require('supertest');
const User = require('../src/models/User');
const Plant = require('../src/models/Plant');
const Reminder = require('../src/models/Reminder');

describe('提醒服务CRUD API', () => {
  let app;
  let authToken;
  let testUserId;
  let testPlantId;

  beforeAll(async () => {
    try {
      app = require('../src/app');
      User.clearMockUsers();
      Plant.clearMockPlants();

      // 注册测试用户并获取认证令牌
      const userData = {
        username: 'reminderuser',
        email: '<EMAIL>',
        password: 'password123',
        confirmPassword: 'password123'
      };

      const registerResponse = await request(app)
        .post('/api/v1/auth/register')
        .send(userData);

      if (registerResponse.status === 201) {
        authToken = registerResponse.body.data.token;
        testUserId = registerResponse.body.data.user.id;
      }

      // 创建测试植物
      if (authToken) {
        const plantData = {
          name: '测试植物',
          species: 'Test Plant',
          description: '用于提醒测试',
          location: '测试位置'
        };

        const plantResponse = await request(app)
          .post('/api/v1/plants')
          .set('Authorization', `Bearer ${authToken}`)
          .send(plantData);

        if (plantResponse.status === 201) {
          testPlantId = plantResponse.body.data.plant.id;
        }
      }
    } catch (error) {
      console.log('App not found - this is expected for TDD red phase');
    }
  });

  beforeEach(async () => {
    // 每个测试前清除提醒模拟数据
    if (process.env.NODE_ENV === 'test') {
      Plant.clearMockPlants();
      Reminder.clearMockReminders();

      // 重新创建测试植物
      if (authToken) {
        const plantData = {
          name: '测试植物',
          species: 'Test Plant',
          description: '用于提醒测试',
          location: '测试位置'
        };

        const plantResponse = await request(app)
          .post('/api/v1/plants')
          .set('Authorization', `Bearer ${authToken}`)
          .send(plantData);

        if (plantResponse.status === 201) {
          testPlantId = plantResponse.body.data.plant.id;
        }
      }
    }
  });

  afterAll(async () => {
    if (app && app.close) {
      await app.close();
    }
  });

  describe('创建提醒 POST /api/v1/reminders', () => {
    test('应该成功创建新提醒', async () => {
      if (!app || !authToken || !testPlantId) {
        expect(true).toBe(false); // TDD红色阶段
        return;
      }

      const reminderData = {
        plantId: testPlantId,
        type: 'watering',
        title: '浇水提醒',
        description: '记得给植物浇水',
        frequency: 'daily',
        frequencyValue: 1,
        startDate: '2024-01-20T09:00:00Z',
        isActive: true
      };

      const response = await request(app)
        .post('/api/v1/reminders')
        .set('Authorization', `Bearer ${authToken}`)
        .send(reminderData)
        .expect(201);

      expect(response.body).toHaveProperty('success', true);
      expect(response.body).toHaveProperty('message');
      expect(response.body).toHaveProperty('data');
      expect(response.body.data).toHaveProperty('reminder');
      expect(response.body.data.reminder).toHaveProperty('id');
      expect(response.body.data.reminder).toHaveProperty('type', 'watering');
      expect(response.body.data.reminder).toHaveProperty('plantId', testPlantId);
      expect(response.body.data.reminder).toHaveProperty('userId', testUserId);
    });

    test('应该验证提醒类型', async () => {
      if (!app || !authToken || !testPlantId) {
        expect(true).toBe(false);
        return;
      }

      const reminderData = {
        plantId: testPlantId,
        type: 'invalid_type',
        title: '无效类型提醒',
        frequency: 'daily'
      };

      const response = await request(app)
        .post('/api/v1/reminders')
        .set('Authorization', `Bearer ${authToken}`)
        .send(reminderData)
        .expect(400);

      expect(response.body).toHaveProperty('error');
      expect(response.body.error.message).toContain('提醒类型');
    });

    test('应该验证植物存在性', async () => {
      if (!app || !authToken) {
        expect(true).toBe(false);
        return;
      }

      const reminderData = {
        plantId: 999,
        type: 'watering',
        title: '给不存在植物的提醒',
        frequency: 'daily'
      };

      const response = await request(app)
        .post('/api/v1/reminders')
        .set('Authorization', `Bearer ${authToken}`)
        .send(reminderData)
        .expect(400);

      expect(response.body).toHaveProperty('error');
      expect(response.body.error.message).toContain('植物不存在');
    });
  });

  describe('获取提醒列表 GET /api/v1/reminders', () => {
    test('应该返回用户的提醒列表', async () => {
      if (!app || !authToken || !testPlantId) {
        expect(true).toBe(false);
        return;
      }

      // 先创建一个提醒
      const reminderData = {
        plantId: testPlantId,
        type: 'fertilizing',
        title: '施肥提醒',
        frequency: 'weekly',
        frequencyValue: 1
      };

      await request(app)
        .post('/api/v1/reminders')
        .set('Authorization', `Bearer ${authToken}`)
        .send(reminderData)
        .expect(201);

      // 获取提醒列表
      const response = await request(app)
        .get('/api/v1/reminders')
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200);

      expect(response.body).toHaveProperty('success', true);
      expect(response.body).toHaveProperty('data');
      expect(response.body.data).toHaveProperty('reminders');
      expect(Array.isArray(response.body.data.reminders)).toBe(true);
      expect(response.body.data.reminders.length).toBeGreaterThan(0);
    });

    test('应该支持按植物ID筛选', async () => {
      if (!app || !authToken || !testPlantId) {
        expect(true).toBe(false);
        return;
      }

      const response = await request(app)
        .get(`/api/v1/reminders?plantId=${testPlantId}`)
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200);

      expect(response.body).toHaveProperty('success', true);
      expect(response.body.data).toHaveProperty('reminders');
    });

    test('应该支持按提醒类型筛选', async () => {
      if (!app || !authToken) {
        expect(true).toBe(false);
        return;
      }

      const response = await request(app)
        .get('/api/v1/reminders?type=watering')
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200);

      expect(response.body).toHaveProperty('success', true);
      expect(response.body.data).toHaveProperty('reminders');
    });

    test('应该支持按状态筛选', async () => {
      if (!app || !authToken) {
        expect(true).toBe(false);
        return;
      }

      const response = await request(app)
        .get('/api/v1/reminders?isActive=true')
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200);

      expect(response.body).toHaveProperty('success', true);
      expect(response.body.data).toHaveProperty('reminders');
    });
  });

  describe('更新提醒状态 PUT /api/v1/reminders/:id/status', () => {
    test('应该成功更新提醒状态', async () => {
      if (!app || !authToken || !testPlantId) {
        expect(true).toBe(false);
        return;
      }

      // 先创建一个提醒
      const reminderData = {
        plantId: testPlantId,
        type: 'pruning',
        title: '修剪提醒',
        frequency: 'monthly',
        isActive: true
      };

      const createResponse = await request(app)
        .post('/api/v1/reminders')
        .set('Authorization', `Bearer ${authToken}`)
        .send(reminderData)
        .expect(201);

      const reminderId = createResponse.body.data.reminder.id;

      // 更新提醒状态
      const response = await request(app)
        .put(`/api/v1/reminders/${reminderId}/status`)
        .set('Authorization', `Bearer ${authToken}`)
        .send({ isActive: false })
        .expect(200);

      expect(response.body).toHaveProperty('success', true);
      expect(response.body.data.reminder).toHaveProperty('isActive', false);
    });
  });

  describe('获取今日提醒 GET /api/v1/reminders/today', () => {
    test('应该返回今日的提醒列表', async () => {
      if (!app || !authToken) {
        expect(true).toBe(false);
        return;
      }

      const response = await request(app)
        .get('/api/v1/reminders/today')
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200);

      expect(response.body).toHaveProperty('success', true);
      expect(response.body.data).toHaveProperty('reminders');
      expect(Array.isArray(response.body.data.reminders)).toBe(true);
    });
  });

  describe('批量操作提醒 POST /api/v1/reminders/batch', () => {
    test('应该支持批量创建提醒', async () => {
      if (!app || !authToken || !testPlantId) {
        expect(true).toBe(false);
        return;
      }

      const batchData = {
        plantId: testPlantId,
        reminders: [
          {
            type: 'watering',
            title: '浇水提醒',
            frequency: 'daily'
          },
          {
            type: 'fertilizing',
            title: '施肥提醒',
            frequency: 'weekly'
          }
        ]
      };

      const response = await request(app)
        .post('/api/v1/reminders/batch')
        .set('Authorization', `Bearer ${authToken}`)
        .send(batchData)
        .expect(201);

      expect(response.body).toHaveProperty('success', true);
      expect(response.body.data).toHaveProperty('reminders');
      expect(response.body.data.reminders).toHaveLength(2);
    });
  });

  describe('删除提醒 DELETE /api/v1/reminders/:id', () => {
    test('应该成功删除提醒', async () => {
      if (!app || !authToken || !testPlantId) {
        expect(true).toBe(false);
        return;
      }

      // 先创建一个提醒
      const reminderData = {
        plantId: testPlantId,
        type: 'cleaning',
        title: '清洁提醒',
        frequency: 'weekly'
      };

      const createResponse = await request(app)
        .post('/api/v1/reminders')
        .set('Authorization', `Bearer ${authToken}`)
        .send(reminderData)
        .expect(201);

      const reminderId = createResponse.body.data.reminder.id;

      // 删除提醒
      const response = await request(app)
        .delete(`/api/v1/reminders/${reminderId}`)
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200);

      expect(response.body).toHaveProperty('success', true);
      expect(response.body).toHaveProperty('message');

      // 验证提醒已被删除
      await request(app)
        .get(`/api/v1/reminders/${reminderId}`)
        .set('Authorization', `Bearer ${authToken}`)
        .expect(404);
    });
  });
});
