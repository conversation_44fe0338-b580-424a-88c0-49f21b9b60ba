/**
 * 认证路由
 * 处理用户认证相关的API端点
 */

const express = require('express');
const router = express.Router();

const AuthController = require('../controllers/authController');
const { authenticateToken } = require('../middlewares/auth');
const { 
  validate, 
  registerValidation, 
  loginValidation, 
  refreshTokenValidation 
} = require('../middlewares/validation');

/**
 * 用户注册
 * POST /api/v1/auth/register
 */
router.post('/register', 
  validate(registerValidation),
  AuthController.register
);

/**
 * 用户登录
 * POST /api/v1/auth/login
 */
router.post('/login', 
  validate(loginValidation),
  AuthController.login
);

/**
 * 刷新令牌
 * POST /api/v1/auth/refresh
 */
router.post('/refresh', 
  validate(refreshTokenValidation),
  AuthController.refreshToken
);

/**
 * 获取用户资料
 * GET /api/v1/auth/profile
 */
router.get('/profile', 
  authenticateToken,
  AuthController.getProfile
);

/**
 * 更新用户资料
 * PUT /api/v1/auth/profile
 */
router.put('/profile', 
  authenticateToken,
  AuthController.updateProfile
);

/**
 * 用户登出
 * POST /api/v1/auth/logout
 */
router.post('/logout', 
  authenticateToken,
  AuthController.logout
);

/**
 * 验证令牌（用于前端检查令牌有效性）
 * GET /api/v1/auth/verify
 */
router.get('/verify', 
  authenticateToken,
  (req, res) => {
    res.json({
      success: true,
      message: '令牌有效',
      data: {
        user: req.user.toSafeJSON()
      }
    });
  }
);

module.exports = router;
