/**
 * 输入验证中间件
 * 使用Joi进行请求数据验证
 */

const Jo<PERSON> = require('joi');

/**
 * 创建验证中间件
 */
const validate = (schema) => {
  return (req, res, next) => {
    const { error, value } = schema.validate(req.body, {
      abortEarly: false, // 返回所有错误
      stripUnknown: true // 移除未知字段
    });

    if (error) {
      const errorMessages = error.details.map(detail => detail.message);
      return res.status(400).json({
        error: {
          message: errorMessages.join('; '),
          status: 400,
          details: errorMessages
        }
      });
    }

    // 将验证后的数据替换原始数据
    req.body = value;
    next();
  };
};

/**
 * 用户注册验证规则
 */
const registerValidation = Joi.object({
  username: Joi.string()
    .alphanum()
    .min(3)
    .max(30)
    .required()
    .messages({
      'string.alphanum': '用户名只能包含字母和数字',
      'string.min': '用户名至少需要3个字符',
      'string.max': '用户名不能超过30个字符',
      'any.required': '用户名是必填项'
    }),

  email: Joi.string()
    .email()
    .required()
    .messages({
      'string.email': '请输入有效的邮箱地址',
      'any.required': '邮箱是必填项'
    }),

  password: Joi.string()
    .min(6)
    .max(128)
    .required()
    .messages({
      'string.min': '密码至少需要6个字符',
      'string.max': '密码不能超过128个字符',
      'any.required': '密码是必填项'
    }),

  confirmPassword: Joi.string()
    .valid(Joi.ref('password'))
    .required()
    .messages({
      'any.only': '密码确认不匹配',
      'any.required': '密码确认是必填项'
    })
});

/**
 * 用户登录验证规则
 */
const loginValidation = Joi.object({
  email: Joi.string()
    .email()
    .required()
    .messages({
      'string.email': '请输入有效的邮箱地址',
      'any.required': '邮箱是必填项'
    }),

  password: Joi.string()
    .required()
    .messages({
      'any.required': '密码是必填项'
    })
});

/**
 * 刷新令牌验证规则
 */
const refreshTokenValidation = Joi.object({
  refreshToken: Joi.string()
    .required()
    .messages({
      'any.required': '刷新令牌是必填项'
    })
});

/**
 * 密码重置请求验证规则
 */
const passwordResetRequestValidation = Joi.object({
  email: Joi.string()
    .email()
    .required()
    .messages({
      'string.email': '请输入有效的邮箱地址',
      'any.required': '邮箱是必填项'
    })
});

/**
 * 密码重置验证规则
 */
const passwordResetValidation = Joi.object({
  token: Joi.string()
    .required()
    .messages({
      'any.required': '重置令牌是必填项'
    }),

  password: Joi.string()
    .min(6)
    .max(128)
    .required()
    .messages({
      'string.min': '密码至少需要6个字符',
      'string.max': '密码不能超过128个字符',
      'any.required': '密码是必填项'
    }),

  confirmPassword: Joi.string()
    .valid(Joi.ref('password'))
    .required()
    .messages({
      'any.only': '密码确认不匹配',
      'any.required': '密码确认是必填项'
    })
});

/**
 * 植物创建验证规则
 */
const plantCreateValidation = Joi.object({
  name: Joi.string()
    .min(1)
    .max(100)
    .required()
    .messages({
      'string.min': '植物名称不能为空',
      'string.max': '植物名称不能超过100个字符',
      'any.required': '植物名称是必填项'
    }),

  species: Joi.string()
    .max(200)
    .allow('')
    .messages({
      'string.max': '植物种类不能超过200个字符'
    }),

  description: Joi.string()
    .max(1000)
    .allow('')
    .messages({
      'string.max': '植物描述不能超过1000个字符'
    }),

  location: Joi.string()
    .max(100)
    .allow('')
    .messages({
      'string.max': '植物位置不能超过100个字符'
    }),

  acquiredDate: Joi.date()
    .iso()
    .max('now')
    .messages({
      'date.format': '获得日期格式不正确',
      'date.max': '获得日期不能是未来时间'
    }),

  image: Joi.string()
    .max(10485760) // 10MB base64 limit
    .allow('')
    .messages({
      'string.max': '图片数据过大，请压缩后重试'
    })
});

/**
 * 植物更新验证规则
 */
const plantUpdateValidation = Joi.object({
  name: Joi.string()
    .min(1)
    .max(100)
    .messages({
      'string.min': '植物名称不能为空',
      'string.max': '植物名称不能超过100个字符'
    }),

  species: Joi.string()
    .max(200)
    .allow('')
    .messages({
      'string.max': '植物种类不能超过200个字符'
    }),

  description: Joi.string()
    .max(1000)
    .allow('')
    .messages({
      'string.max': '植物描述不能超过1000个字符'
    }),

  location: Joi.string()
    .max(100)
    .allow('')
    .messages({
      'string.max': '植物位置不能超过100个字符'
    }),

  acquiredDate: Joi.date()
    .iso()
    .max('now')
    .messages({
      'date.format': '获得日期格式不正确',
      'date.max': '获得日期不能是未来时间'
    }),

  image: Joi.string()
    .max(10485760)
    .allow('')
    .messages({
      'string.max': '图片数据过大，请压缩后重试'
    })
});

/**
 * 护理记录创建验证规则
 */
const careRecordCreateValidation = Joi.object({
  plantId: Joi.number()
    .integer()
    .positive()
    .required()
    .messages({
      'number.base': '植物ID必须是数字',
      'number.integer': '植物ID必须是整数',
      'number.positive': '植物ID必须是正数',
      'any.required': '植物ID是必填项'
    }),

  type: Joi.string()
    .valid('watering', 'fertilizing', 'pruning', 'repotting', 'pest_control', 'cleaning', 'observation', 'other')
    .required()
    .messages({
      'any.only': '护理类型无效',
      'any.required': '护理类型是必填项'
    }),

  description: Joi.string()
    .max(500)
    .allow('')
    .messages({
      'string.max': '护理描述不能超过500个字符'
    }),

  amount: Joi.string()
    .max(100)
    .allow('')
    .messages({
      'string.max': '护理用量不能超过100个字符'
    }),

  notes: Joi.string()
    .max(1000)
    .allow('')
    .messages({
      'string.max': '护理备注不能超过1000个字符'
    }),

  careDate: Joi.date()
    .iso()
    .max('now')
    .messages({
      'date.format': '护理日期格式不正确',
      'date.max': '护理日期不能是未来时间'
    })
});

/**
 * 护理记录更新验证规则
 */
const careRecordUpdateValidation = Joi.object({
  type: Joi.string()
    .valid('watering', 'fertilizing', 'pruning', 'repotting', 'pest_control', 'cleaning', 'observation', 'other')
    .messages({
      'any.only': '护理类型无效'
    }),

  description: Joi.string()
    .max(500)
    .allow('')
    .messages({
      'string.max': '护理描述不能超过500个字符'
    }),

  amount: Joi.string()
    .max(100)
    .allow('')
    .messages({
      'string.max': '护理用量不能超过100个字符'
    }),

  notes: Joi.string()
    .max(1000)
    .allow('')
    .messages({
      'string.max': '护理备注不能超过1000个字符'
    }),

  careDate: Joi.date()
    .iso()
    .max('now')
    .messages({
      'date.format': '护理日期格式不正确',
      'date.max': '护理日期不能是未来时间'
    })
});

/**
 * 生长记录创建验证规则
 */
const growthRecordCreateValidation = Joi.object({
  plantId: Joi.number()
    .integer()
    .positive()
    .required()
    .messages({
      'number.base': '植物ID必须是数字',
      'number.integer': '植物ID必须是整数',
      'number.positive': '植物ID必须是正数',
      'any.required': '植物ID是必填项'
    }),

  height: Joi.number()
    .positive()
    .precision(2)
    .messages({
      'number.base': '高度必须是数字',
      'number.positive': '高度必须是正数'
    }),

  width: Joi.number()
    .positive()
    .precision(2)
    .messages({
      'number.base': '宽度必须是数字',
      'number.positive': '宽度必须是正数'
    }),

  leafCount: Joi.number()
    .integer()
    .min(0)
    .messages({
      'number.base': '叶片数量必须是数字',
      'number.integer': '叶片数量必须是整数',
      'number.min': '叶片数量不能为负数'
    }),

  notes: Joi.string()
    .max(1000)
    .allow('')
    .messages({
      'string.max': '备注不能超过1000个字符'
    }),

  images: Joi.array()
    .items(Joi.string().max(10485760))
    .max(10)
    .messages({
      'array.max': '最多只能上传10张图片',
      'string.max': '单张图片数据过大，请压缩后重试'
    }),

  recordDate: Joi.date()
    .iso()
    .max('now')
    .messages({
      'date.format': '记录日期格式不正确',
      'date.max': '记录日期不能是未来时间'
    })
});

/**
 * 生长记录更新验证规则
 */
const growthRecordUpdateValidation = Joi.object({
  height: Joi.number()
    .positive()
    .precision(2)
    .messages({
      'number.base': '高度必须是数字',
      'number.positive': '高度必须是正数'
    }),

  width: Joi.number()
    .positive()
    .precision(2)
    .messages({
      'number.base': '宽度必须是数字',
      'number.positive': '宽度必须是正数'
    }),

  leafCount: Joi.number()
    .integer()
    .min(0)
    .messages({
      'number.base': '叶片数量必须是数字',
      'number.integer': '叶片数量必须是整数',
      'number.min': '叶片数量不能为负数'
    }),

  notes: Joi.string()
    .max(1000)
    .allow('')
    .messages({
      'string.max': '备注不能超过1000个字符'
    }),

  images: Joi.array()
    .items(Joi.string().max(10485760))
    .max(10)
    .messages({
      'array.max': '最多只能上传10张图片',
      'string.max': '单张图片数据过大，请压缩后重试'
    }),

  recordDate: Joi.date()
    .iso()
    .max('now')
    .messages({
      'date.format': '记录日期格式不正确',
      'date.max': '记录日期不能是未来时间'
    })
});

/**
 * 提醒创建验证规则
 */
const reminderCreateValidation = Joi.object({
  plantId: Joi.number()
    .integer()
    .positive()
    .required()
    .messages({
      'number.base': '植物ID必须是数字',
      'number.integer': '植物ID必须是整数',
      'number.positive': '植物ID必须是正数',
      'any.required': '植物ID是必填项'
    }),

  type: Joi.string()
    .valid('watering', 'fertilizing', 'pruning', 'repotting', 'pest_control', 'cleaning', 'observation', 'other')
    .required()
    .messages({
      'any.only': '提醒类型无效',
      'any.required': '提醒类型是必填项'
    }),

  title: Joi.string()
    .min(1)
    .max(200)
    .required()
    .messages({
      'string.min': '提醒标题不能为空',
      'string.max': '提醒标题不能超过200个字符',
      'any.required': '提醒标题是必填项'
    }),

  description: Joi.string()
    .max(1000)
    .allow('')
    .messages({
      'string.max': '提醒描述不能超过1000个字符'
    }),

  frequency: Joi.string()
    .valid('daily', 'weekly', 'monthly', 'yearly', 'custom')
    .required()
    .messages({
      'any.only': '频率类型无效',
      'any.required': '频率类型是必填项'
    }),

  frequencyValue: Joi.number()
    .integer()
    .min(1)
    .max(365)
    .messages({
      'number.base': '频率值必须是数字',
      'number.integer': '频率值必须是整数',
      'number.min': '频率值必须大于0',
      'number.max': '频率值不能超过365'
    }),

  startDate: Joi.date()
    .iso()
    .messages({
      'date.format': '开始日期格式不正确'
    }),

  isActive: Joi.boolean()
    .messages({
      'boolean.base': '激活状态必须是布尔值'
    })
});

/**
 * 提醒更新验证规则
 */
const reminderUpdateValidation = Joi.object({
  type: Joi.string()
    .valid('watering', 'fertilizing', 'pruning', 'repotting', 'pest_control', 'cleaning', 'observation', 'other')
    .messages({
      'any.only': '提醒类型无效'
    }),

  title: Joi.string()
    .min(1)
    .max(200)
    .messages({
      'string.min': '提醒标题不能为空',
      'string.max': '提醒标题不能超过200个字符'
    }),

  description: Joi.string()
    .max(1000)
    .allow('')
    .messages({
      'string.max': '提醒描述不能超过1000个字符'
    }),

  frequency: Joi.string()
    .valid('daily', 'weekly', 'monthly', 'yearly', 'custom')
    .messages({
      'any.only': '频率类型无效'
    }),

  frequencyValue: Joi.number()
    .integer()
    .min(1)
    .max(365)
    .messages({
      'number.base': '频率值必须是数字',
      'number.integer': '频率值必须是整数',
      'number.min': '频率值必须大于0',
      'number.max': '频率值不能超过365'
    }),

  startDate: Joi.date()
    .iso()
    .messages({
      'date.format': '开始日期格式不正确'
    }),

  isActive: Joi.boolean()
    .messages({
      'boolean.base': '激活状态必须是布尔值'
    })
});

/**
 * 提醒状态更新验证规则
 */
const reminderStatusValidation = Joi.object({
  isActive: Joi.boolean()
    .required()
    .messages({
      'boolean.base': '激活状态必须是布尔值',
      'any.required': '激活状态是必填项'
    })
});

/**
 * 批量提醒创建验证规则
 */
const batchReminderValidation = Joi.object({
  plantId: Joi.number()
    .integer()
    .positive()
    .required()
    .messages({
      'number.base': '植物ID必须是数字',
      'number.integer': '植物ID必须是整数',
      'number.positive': '植物ID必须是正数',
      'any.required': '植物ID是必填项'
    }),

  reminders: Joi.array()
    .items(Joi.object({
      type: Joi.string()
        .valid('watering', 'fertilizing', 'pruning', 'repotting', 'pest_control', 'cleaning', 'observation', 'other')
        .required(),
      title: Joi.string().min(1).max(200).required(),
      description: Joi.string().max(1000).allow(''),
      frequency: Joi.string()
        .valid('daily', 'weekly', 'monthly', 'yearly', 'custom')
        .required(),
      frequencyValue: Joi.number().integer().min(1).max(365),
      startDate: Joi.date().iso(),
      isActive: Joi.boolean()
    }))
    .min(1)
    .max(20)
    .required()
    .messages({
      'array.min': '至少需要一个提醒',
      'array.max': '最多只能批量创建20个提醒',
      'any.required': '提醒列表是必填项'
    })
});

module.exports = {
  validate,
  registerValidation,
  loginValidation,
  refreshTokenValidation,
  passwordResetRequestValidation,
  passwordResetValidation,
  plantCreateValidation,
  plantUpdateValidation,
  careRecordCreateValidation,
  careRecordUpdateValidation,
  growthRecordCreateValidation,
  growthRecordUpdateValidation,
  reminderCreateValidation,
  reminderUpdateValidation,
  reminderStatusValidation,
  batchReminderValidation
};
