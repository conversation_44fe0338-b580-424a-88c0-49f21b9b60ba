/**
 * 认证控制器
 * 处理用户注册、登录、令牌刷新等认证相关操作
 */

const User = require('../models/User');
const JWTUtils = require('../utils/jwt');
const { logger } = require('../middlewares/errorHandler');

class AuthController {
  /**
   * 用户注册
   */
  static async register(req, res) {
    try {
      const { username, email, password } = req.body;

      // 检查邮箱是否已存在
      const existingUser = await User.findByEmail(email);
      if (existingUser) {
        return res.status(400).json({
          error: {
            message: '邮箱已存在',
            status: 400
          }
        });
      }

      // 创建新用户
      const newUser = await User.create({
        username,
        email,
        password
      });

      // 生成令牌
      const tokens = JWTUtils.generateTokenPair(newUser);

      logger.info(`新用户注册成功: ${email}`);

      res.status(201).json({
        success: true,
        message: '用户注册成功',
        data: {
          user: newUser.toSafeJSON(),
          ...tokens
        }
      });
    } catch (error) {
      logger.error('用户注册失败:', error);
      
      if (error.message === '邮箱已存在' || error.message === '用户名已存在') {
        return res.status(400).json({
          error: {
            message: error.message,
            status: 400
          }
        });
      }

      res.status(500).json({
        error: {
          message: '注册失败，请稍后重试',
          status: 500
        }
      });
    }
  }

  /**
   * 用户登录
   */
  static async login(req, res) {
    try {
      const { email, password } = req.body;

      // 查找用户
      const user = await User.findByEmail(email);
      if (!user) {
        return res.status(401).json({
          error: {
            message: '邮箱或密码错误',
            status: 401
          }
        });
      }

      // 验证密码
      const isValidPassword = await user.validatePassword(password);
      if (!isValidPassword) {
        return res.status(401).json({
          error: {
            message: '邮箱或密码错误',
            status: 401
          }
        });
      }

      // 生成令牌
      const tokens = JWTUtils.generateTokenPair(user);

      logger.info(`用户登录成功: ${email}`);

      res.json({
        success: true,
        message: '登录成功',
        data: {
          user: user.toSafeJSON(),
          ...tokens
        }
      });
    } catch (error) {
      logger.error('用户登录失败:', error);
      
      res.status(500).json({
        error: {
          message: '登录失败，请稍后重试',
          status: 500
        }
      });
    }
  }

  /**
   * 刷新令牌
   */
  static async refreshToken(req, res) {
    try {
      const { refreshToken } = req.body;

      // 验证刷新令牌
      const decoded = JWTUtils.verifyRefreshToken(refreshToken);

      // 查找用户
      const user = await User.findById(decoded.userId);
      if (!user) {
        return res.status(401).json({
          error: {
            message: '用户不存在',
            status: 401
          }
        });
      }

      // 生成新的令牌对
      const tokens = JWTUtils.generateTokenPair(user);

      logger.info(`令牌刷新成功: ${user.email}`);

      res.json({
        success: true,
        message: '令牌刷新成功',
        data: tokens
      });
    } catch (error) {
      logger.error('令牌刷新失败:', error);
      
      if (error.message.includes('无效的刷新令牌')) {
        return res.status(401).json({
          error: {
            message: '无效的刷新令牌',
            status: 401
          }
        });
      }

      res.status(500).json({
        error: {
          message: '令牌刷新失败，请稍后重试',
          status: 500
        }
      });
    }
  }

  /**
   * 获取用户资料
   */
  static async getProfile(req, res) {
    try {
      // req.user 由认证中间件设置
      const user = req.user;

      res.json({
        success: true,
        message: '获取用户资料成功',
        data: {
          user: user.toSafeJSON()
        }
      });
    } catch (error) {
      logger.error('获取用户资料失败:', error);
      
      res.status(500).json({
        error: {
          message: '获取用户资料失败，请稍后重试',
          status: 500
        }
      });
    }
  }

  /**
   * 更新用户资料
   */
  static async updateProfile(req, res) {
    try {
      const { username } = req.body;
      const user = req.user;

      // 这里可以添加更新用户资料的逻辑
      // 目前只是返回当前用户信息

      res.json({
        success: true,
        message: '用户资料更新成功',
        data: {
          user: user.toSafeJSON()
        }
      });
    } catch (error) {
      logger.error('更新用户资料失败:', error);
      
      res.status(500).json({
        error: {
          message: '更新用户资料失败，请稍后重试',
          status: 500
        }
      });
    }
  }

  /**
   * 用户登出
   */
  static async logout(req, res) {
    try {
      // 在实际应用中，这里可以将令牌加入黑名单
      // 目前只是返回成功消息

      logger.info(`用户登出: ${req.user.email}`);

      res.json({
        success: true,
        message: '登出成功'
      });
    } catch (error) {
      logger.error('用户登出失败:', error);
      
      res.status(500).json({
        error: {
          message: '登出失败，请稍后重试',
          status: 500
        }
      });
    }
  }
}

module.exports = AuthController;
