/**
 * 护理记录路由
 * 处理护理记录相关的API端点
 */

const express = require('express');
const router = express.Router();

const CareRecordController = require('../controllers/careRecordController');
const { authenticateToken } = require('../middlewares/auth');
const { 
  validate, 
  careRecordCreateValidation, 
  careRecordUpdateValidation 
} = require('../middlewares/validation');

/**
 * 获取护理记录统计信息
 * GET /api/v1/care-records/stats
 */
router.get('/stats', 
  authenticateToken,
  CareRecordController.getCareRecordStats
);

/**
 * 创建护理记录
 * POST /api/v1/care-records
 */
router.post('/', 
  authenticateToken,
  validate(careRecordCreateValidation),
  CareRecordController.createCareRecord
);

/**
 * 获取护理记录列表
 * GET /api/v1/care-records
 */
router.get('/', 
  authenticateToken,
  CareRecordController.getCareRecords
);

/**
 * 获取单个护理记录详情
 * GET /api/v1/care-records/:id
 */
router.get('/:id', 
  authenticateToken,
  CareRecordController.getCareRecordById
);

/**
 * 更新护理记录
 * PUT /api/v1/care-records/:id
 */
router.put('/:id', 
  authenticateToken,
  validate(careRecordUpdateValidation),
  CareRecordController.updateCareRecord
);

/**
 * 删除护理记录
 * DELETE /api/v1/care-records/:id
 */
router.delete('/:id', 
  authenticateToken,
  CareRecordController.deleteCareRecord
);

module.exports = router;
