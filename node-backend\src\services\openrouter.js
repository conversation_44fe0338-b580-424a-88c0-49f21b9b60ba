/**
 * OpenRouter API 服务模块
 *
 * 提供与OpenRouter API的集成，支持多模态植物识别
 * 包括图像、文本和分子数据的处理
 */

const axios = require('axios');
const FormData = require('form-data');
const fs = require('fs');
const path = require('path');
const logger = require('../utils/logger');

class OpenRouterService {
  constructor() {
    this.apiKey = process.env.OPENROUTER_API_KEY;
    this.baseURL = process.env.OPENROUTER_BASE_URL || 'https://openrouter.ai/api/v1';
    this.defaultModel = process.env.OPENROUTER_DEFAULT_MODEL || 'anthropic/claude-3-sonnet';
    this.maxRetries = parseInt(process.env.OPENROUTER_MAX_RETRIES) || 3;
    this.timeout = parseInt(process.env.OPENROUTER_TIMEOUT) || 30000;

    if (!this.apiKey) {
      logger.warn('OpenRouter API key not configured');
    }

    // 配置axios实例
    this.client = axios.create({
      baseURL: this.baseURL,
      timeout: this.timeout,
      headers: {
        'Authorization': `Bearer ${this.apiKey}`,
        'Content-Type': 'application/json',
        'HTTP-Referer': process.env.APP_URL || 'http://localhost:3000',
        'X-Title': 'Plant Care App'
      }
    });

    // 添加请求拦截器
    this.client.interceptors.request.use(
      (config) => {
        logger.info(`OpenRouter API Request: ${config.method?.toUpperCase()} ${config.url}`);
        return config;
      },
      (error) => {
        logger.error('OpenRouter API Request Error:', error);
        return Promise.reject(error);
      }
    );

    // 添加响应拦截器
    this.client.interceptors.response.use(
      (response) => {
        logger.info(`OpenRouter API Response: ${response.status} ${response.statusText}`);
        return response;
      },
      (error) => {
        logger.error('OpenRouter API Response Error:', {
          status: error.response?.status,
          statusText: error.response?.statusText,
          data: error.response?.data
        });
        return Promise.reject(error);
      }
    );
  }

  /**
   * 多模态植物识别
   * @param {Object} input - 输入数据
   * @param {string} input.imageBase64 - base64编码的图像数据
   * @param {string} input.textDescription - 文本描述
   * @param {string} input.molecularData - 分子数据（基因序列等）
   * @param {Object} options - 选项
   * @returns {Promise<Object>} 识别结果
   */
  async identifyPlant(input, options = {}) {
    try {
      const { imageBase64, textDescription, molecularData } = input;
      const { model = this.defaultModel, temperature = 0.7 } = options;

      // 构建多模态提示
      const prompt = this._buildMultimodalPrompt(textDescription, molecularData);

      // 构建消息数组
      const messages = [
        {
          role: 'system',
          content: this._getSystemPrompt()
        },
        {
          role: 'user',
          content: this._buildUserMessage(prompt, imageBase64)
        }
      ];

      // 发送请求
      const response = await this._makeRequest('/chat/completions', {
        model,
        messages,
        temperature,
        max_tokens: 2000,
        stream: false
      });

      // 解析响应
      const result = this._parseIdentificationResult(response.data);

      // 记录成功的识别
      logger.info('Plant identification completed successfully', {
        hasImage: !!imageBase64,
        hasText: !!textDescription,
        hasMolecular: !!molecularData,
        confidence: result.confidence
      });

      return result;

    } catch (error) {
      logger.error('Plant identification failed:', error);
      throw this._handleError(error);
    }
  }

  /**
   * 获取植物护理建议
   * @param {string} plantName - 植物名称
   * @param {Object} conditions - 环境条件
   * @returns {Promise<Object>} 护理建议
   */
  async getCareAdvice(plantName, conditions = {}) {
    try {
      const prompt = this._buildCareAdvicePrompt(plantName, conditions);

      const messages = [
        {
          role: 'system',
          content: 'You are a plant care expert. Provide detailed, practical care advice for plants.'
        },
        {
          role: 'user',
          content: prompt
        }
      ];

      const response = await this._makeRequest('/chat/completions', {
        model: this.defaultModel,
        messages,
        temperature: 0.5,
        max_tokens: 1500
      });

      return this._parseCareAdvice(response.data);

    } catch (error) {
      logger.error('Care advice generation failed:', error);
      throw this._handleError(error);
    }
  }

  /**
   * 处理图像数据
   * @param {Buffer|string} imageData - 图像数据
   * @returns {string} base64编码的图像
   */
  processImage(imageData) {
    try {
      let base64Data;

      if (Buffer.isBuffer(imageData)) {
        base64Data = imageData.toString('base64');
      } else if (typeof imageData === 'string') {
        // 假设是文件路径
        if (fs.existsSync(imageData)) {
          const buffer = fs.readFileSync(imageData);
          base64Data = buffer.toString('base64');
        } else {
          // 假设已经是base64数据
          base64Data = imageData.replace(/^data:image\/[a-z]+;base64,/, '');
        }
      } else {
        throw new Error('Invalid image data format');
      }

      // 验证base64数据
      if (!this._isValidBase64(base64Data)) {
        throw new Error('Invalid base64 image data');
      }

      return base64Data;

    } catch (error) {
      logger.error('Image processing failed:', error);
      throw error;
    }
  }

  /**
   * 验证分子数据格式
   * @param {string} molecularData - 分子数据
   * @returns {boolean} 是否有效
   */
  validateMolecularData(molecularData) {
    if (!molecularData || typeof molecularData !== 'string') {
      return false;
    }

    // 检查FASTA格式
    if (molecularData.startsWith('>')) {
      return this._validateFastaFormat(molecularData);
    }

    // 检查DNA序列格式
    if (/^[ATCGN]+$/i.test(molecularData.replace(/\s/g, ''))) {
      return true;
    }

    return false;
  }

  /**
   * 构建多模态提示
   * @private
   */
  _buildMultimodalPrompt(textDescription, molecularData) {
    let prompt = 'Please identify this plant based on the provided information:\n\n';

    if (textDescription) {
      prompt += `Text Description: ${textDescription}\n\n`;
    }

    if (molecularData) {
      prompt += `Molecular Data: ${molecularData}\n\n`;
    }

    prompt += 'Please provide a detailed identification including:\n';
    prompt += '1. Scientific name and common name\n';
    prompt += '2. Confidence level (0-100%)\n';
    prompt += '3. Key identifying features\n';
    prompt += '4. Family and genus information\n';
    prompt += '5. Basic care requirements\n';
    prompt += '6. Native habitat information\n\n';
    prompt += 'Format your response as JSON.';

    return prompt;
  }

  /**
   * 构建用户消息
   * @private
   */
  _buildUserMessage(prompt, imageBase64) {
    const content = [
      {
        type: 'text',
        text: prompt
      }
    ];

    if (imageBase64) {
      content.push({
        type: 'image_url',
        image_url: {
          url: `data:image/jpeg;base64,${imageBase64}`
        }
      });
    }

    return content;
  }

  /**
   * 获取系统提示
   * @private
   */
  _getSystemPrompt() {
    return `You are an expert botanist and plant identification specialist with extensive knowledge of:
- Plant taxonomy and classification
- Morphological characteristics of plants
- Molecular biology and phylogenetics
- Plant ecology and distribution
- Horticultural practices

When identifying plants, consider all available information including visual features, textual descriptions, and molecular data. Provide accurate, detailed, and scientifically sound identifications with confidence levels.`;
  }

  /**
   * 构建护理建议提示
   * @private
   */
  _buildCareAdvicePrompt(plantName, conditions) {
    let prompt = `Provide comprehensive care advice for ${plantName}.\n\n`;

    if (conditions.location) {
      prompt += `Location: ${conditions.location}\n`;
    }
    if (conditions.climate) {
      prompt += `Climate: ${conditions.climate}\n`;
    }
    if (conditions.season) {
      prompt += `Season: ${conditions.season}\n`;
    }

    prompt += '\nPlease include advice on:\n';
    prompt += '- Watering schedule and requirements\n';
    prompt += '- Light requirements\n';
    prompt += '- Soil and fertilization\n';
    prompt += '- Temperature and humidity\n';
    prompt += '- Common problems and solutions\n';
    prompt += '- Seasonal care variations\n\n';
    prompt += 'Format your response as JSON.';

    return prompt;
  }

  /**
   * 发送API请求
   * @private
   */
  async _makeRequest(endpoint, data, retryCount = 0) {
    try {
      const response = await this.client.post(endpoint, data);
      return response;
    } catch (error) {
      if (retryCount < this.maxRetries && this._shouldRetry(error)) {
        logger.warn(`Retrying OpenRouter request (${retryCount + 1}/${this.maxRetries})`);
        await this._delay(Math.pow(2, retryCount) * 1000); // 指数退避
        return this._makeRequest(endpoint, data, retryCount + 1);
      }
      throw error;
    }
  }

  /**
   * 解析识别结果
   * @private
   */
  _parseIdentificationResult(responseData) {
    try {
      const content = responseData.choices[0]?.message?.content;
      if (!content) {
        throw new Error('No content in response');
      }

      // 尝试解析JSON响应
      let result;
      try {
        result = JSON.parse(content);
      } catch {
        // 如果不是JSON，尝试从文本中提取信息
        result = this._extractInfoFromText(content);
      }

      return {
        scientificName: result.scientificName || result.scientific_name || '',
        commonName: result.commonName || result.common_name || '',
        confidence: result.confidence || 0,
        features: result.features || result.identifying_features || [],
        family: result.family || '',
        genus: result.genus || '',
        careRequirements: result.careRequirements || result.care_requirements || {},
        habitat: result.habitat || result.native_habitat || '',
        rawResponse: content,
        timestamp: new Date().toISOString()
      };
    } catch (error) {
      logger.error('Failed to parse identification result:', error);
      throw new Error('Failed to parse identification result');
    }
  }

  /**
   * 解析护理建议
   * @private
   */
  _parseCareAdvice(responseData) {
    try {
      const content = responseData.choices[0]?.message?.content;
      if (!content) {
        throw new Error('No content in response');
      }

      let advice;
      try {
        advice = JSON.parse(content);
      } catch {
        advice = { general: content };
      }

      return {
        watering: advice.watering || '',
        light: advice.light || '',
        soil: advice.soil || '',
        temperature: advice.temperature || '',
        humidity: advice.humidity || '',
        fertilization: advice.fertilization || '',
        problems: advice.problems || advice.common_problems || [],
        seasonal: advice.seasonal || advice.seasonal_care || {},
        rawResponse: content,
        timestamp: new Date().toISOString()
      };
    } catch (error) {
      logger.error('Failed to parse care advice:', error);
      throw new Error('Failed to parse care advice');
    }
  }

  /**
   * 处理错误
   * @private
   */
  _handleError(error) {
    if (error.response) {
      const status = error.response.status;
      const message = error.response.data?.error?.message || error.message;

      switch (status) {
        case 401:
          return new Error('OpenRouter API authentication failed');
        case 403:
          return new Error('OpenRouter API access forbidden');
        case 429:
          return new Error('OpenRouter API rate limit exceeded');
        case 500:
          return new Error('OpenRouter API server error');
        default:
          return new Error(`OpenRouter API error: ${message}`);
      }
    }

    if (error.code === 'ECONNABORTED') {
      return new Error('OpenRouter API request timeout');
    }

    return error;
  }

  /**
   * 判断是否应该重试
   * @private
   */
  _shouldRetry(error) {
    if (!error.response) return true; // 网络错误

    const status = error.response.status;
    return status >= 500 || status === 429; // 服务器错误或限流
  }

  /**
   * 延迟函数
   * @private
   */
  _delay(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  /**
   * 验证base64数据
   * @private
   */
  _isValidBase64(str) {
    try {
      // 在Node.js中使用Buffer进行base64验证
      const decoded = Buffer.from(str, 'base64').toString('base64');
      return decoded === str;
    } catch {
      return false;
    }
  }

  /**
   * 验证FASTA格式
   * @private
   */
  _validateFastaFormat(data) {
    const lines = data.split('\n');
    if (!lines[0].startsWith('>')) return false;

    for (let i = 1; i < lines.length; i++) {
      const line = lines[i].trim();
      if (line && !/^[ATCGN]+$/i.test(line)) {
        return false;
      }
    }

    return true;
  }

  /**
   * 从文本中提取信息
   * @private
   */
  _extractInfoFromText(text) {
    // 简单的文本解析逻辑
    const result = {};

    // 提取科学名称
    const scientificMatch = text.match(/scientific name[:\s]+([A-Z][a-z]+ [a-z]+)/i);
    if (scientificMatch) {
      result.scientificName = scientificMatch[1];
    }

    // 提取常用名称
    const commonMatch = text.match(/common name[:\s]+([^.\n]+)/i);
    if (commonMatch) {
      result.commonName = commonMatch[1].trim();
    }

    // 提取置信度
    const confidenceMatch = text.match(/confidence[:\s]+(\d+)%?/i);
    if (confidenceMatch) {
      result.confidence = parseInt(confidenceMatch[1]);
    }

    return result;
  }
}

module.exports = OpenRouterService;
