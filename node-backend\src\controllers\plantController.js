/**
 * 植物控制器
 * 处理植物相关的API请求
 */

const Plant = require('../models/Plant');
const { logger } = require('../middlewares/errorHandler');

class PlantController {
  /**
   * 创建植物
   * POST /api/v1/plants
   */
  static async createPlant(req, res) {
    try {
      const userId = req.user.id;
      const plantData = req.body;

      const newPlant = await Plant.create(plantData, userId);

      logger.info(`用户 ${userId} 创建了新植物: ${newPlant.name}`);

      res.status(201).json({
        success: true,
        message: '植物创建成功',
        data: {
          plant: newPlant.toJSON()
        }
      });
    } catch (error) {
      logger.error('创建植物失败:', error);
      
      res.status(500).json({
        error: {
          message: '创建植物失败，请稍后重试',
          status: 500
        }
      });
    }
  }

  /**
   * 获取植物列表
   * GET /api/v1/plants
   */
  static async getPlants(req, res) {
    try {
      const userId = req.user.id;
      const options = {
        page: parseInt(req.query.page) || 1,
        limit: parseInt(req.query.limit) || 10,
        search: req.query.search
      };

      // 验证分页参数
      if (options.page < 1) options.page = 1;
      if (options.limit < 1 || options.limit > 100) options.limit = 10;

      const result = await Plant.findByUserId(userId, options);

      res.json({
        success: true,
        message: '获取植物列表成功',
        data: {
          plants: result.plants.map(plant => plant.toJSON()),
          pagination: {
            page: result.page,
            limit: result.limit,
            total: result.total,
            totalPages: result.totalPages
          }
        }
      });
    } catch (error) {
      logger.error('获取植物列表失败:', error);
      
      res.status(500).json({
        error: {
          message: '获取植物列表失败，请稍后重试',
          status: 500
        }
      });
    }
  }

  /**
   * 获取单个植物详情
   * GET /api/v1/plants/:id
   */
  static async getPlantById(req, res) {
    try {
      const userId = req.user.id;
      const plantId = req.params.id;

      // 验证植物ID
      if (!plantId || isNaN(plantId)) {
        return res.status(400).json({
          error: {
            message: '无效的植物ID',
            status: 400
          }
        });
      }

      const plant = await Plant.findByIdAndUserId(plantId, userId);

      if (!plant) {
        return res.status(404).json({
          error: {
            message: '植物不存在或不属于当前用户',
            status: 404
          }
        });
      }

      res.json({
        success: true,
        message: '获取植物详情成功',
        data: {
          plant: plant.toJSON()
        }
      });
    } catch (error) {
      logger.error('获取植物详情失败:', error);
      
      res.status(500).json({
        error: {
          message: '获取植物详情失败，请稍后重试',
          status: 500
        }
      });
    }
  }

  /**
   * 更新植物信息
   * PUT /api/v1/plants/:id
   */
  static async updatePlant(req, res) {
    try {
      const userId = req.user.id;
      const plantId = req.params.id;
      const updateData = req.body;

      // 验证植物ID
      if (!plantId || isNaN(plantId)) {
        return res.status(400).json({
          error: {
            message: '无效的植物ID',
            status: 400
          }
        });
      }

      // 检查植物是否存在
      const existingPlant = await Plant.findByIdAndUserId(plantId, userId);
      if (!existingPlant) {
        return res.status(404).json({
          error: {
            message: '植物不存在或不属于当前用户',
            status: 404
          }
        });
      }

      const updatedPlant = await Plant.updateByIdAndUserId(plantId, userId, updateData);

      if (!updatedPlant) {
        return res.status(404).json({
          error: {
            message: '更新失败，植物不存在',
            status: 404
          }
        });
      }

      logger.info(`用户 ${userId} 更新了植物 ${plantId}`);

      res.json({
        success: true,
        message: '植物信息更新成功',
        data: {
          plant: updatedPlant.toJSON()
        }
      });
    } catch (error) {
      logger.error('更新植物失败:', error);
      
      res.status(500).json({
        error: {
          message: '更新植物失败，请稍后重试',
          status: 500
        }
      });
    }
  }

  /**
   * 删除植物
   * DELETE /api/v1/plants/:id
   */
  static async deletePlant(req, res) {
    try {
      const userId = req.user.id;
      const plantId = req.params.id;

      // 验证植物ID
      if (!plantId || isNaN(plantId)) {
        return res.status(400).json({
          error: {
            message: '无效的植物ID',
            status: 400
          }
        });
      }

      // 检查植物是否存在
      const existingPlant = await Plant.findByIdAndUserId(plantId, userId);
      if (!existingPlant) {
        return res.status(404).json({
          error: {
            message: '植物不存在或不属于当前用户',
            status: 404
          }
        });
      }

      const deleted = await Plant.deleteByIdAndUserId(plantId, userId);

      if (!deleted) {
        return res.status(404).json({
          error: {
            message: '删除失败，植物不存在',
            status: 404
          }
        });
      }

      logger.info(`用户 ${userId} 删除了植物 ${plantId}`);

      res.json({
        success: true,
        message: '植物删除成功'
      });
    } catch (error) {
      logger.error('删除植物失败:', error);
      
      res.status(500).json({
        error: {
          message: '删除植物失败，请稍后重试',
          status: 500
        }
      });
    }
  }

  /**
   * 获取植物统计信息
   * GET /api/v1/plants/stats
   */
  static async getPlantStats(req, res) {
    try {
      const userId = req.user.id;

      // 获取用户所有植物
      const result = await Plant.findByUserId(userId, { limit: 1000 });
      const plants = result.plants;

      // 计算统计信息
      const stats = {
        totalPlants: plants.length,
        speciesCount: new Set(plants.map(p => p.species).filter(Boolean)).size,
        locationsCount: new Set(plants.map(p => p.location).filter(Boolean)).size,
        recentlyAdded: plants.filter(p => {
          const addedDate = new Date(p.createdAt);
          const weekAgo = new Date();
          weekAgo.setDate(weekAgo.getDate() - 7);
          return addedDate > weekAgo;
        }).length
      };

      res.json({
        success: true,
        message: '获取植物统计成功',
        data: {
          stats
        }
      });
    } catch (error) {
      logger.error('获取植物统计失败:', error);
      
      res.status(500).json({
        error: {
          message: '获取植物统计失败，请稍后重试',
          status: 500
        }
      });
    }
  }
}

module.exports = PlantController;
