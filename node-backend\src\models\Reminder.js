/**
 * 提醒模型
 * 处理提醒数据的CRUD操作
 */

const databaseManager = require('../config/database');
const Plant = require('./Plant');

class Reminder {
  constructor(reminderData) {
    this.id = reminderData.id;
    this.plantId = reminderData.plant_id || reminderData.plantId;
    this.userId = reminderData.user_id || reminderData.userId;
    this.type = reminderData.type;
    this.title = reminderData.title;
    this.description = reminderData.description;
    this.frequency = reminderData.frequency;
    this.frequencyValue = reminderData.frequency_value || reminderData.frequencyValue;
    this.startDate = reminderData.start_date || reminderData.startDate;
    this.nextDate = reminderData.next_date || reminderData.nextDate;
    this.isActive = reminderData.is_active !== undefined ? reminderData.is_active : reminderData.isActive;
    this.createdAt = reminderData.created_at || reminderData.createdAt;
    this.updatedAt = reminderData.updated_at || reminderData.updatedAt;
  }

  /**
   * 提醒类型枚举
   */
  static get REMINDER_TYPES() {
    return [
      'watering',      // 浇水
      'fertilizing',   // 施肥
      'pruning',       // 修剪
      'repotting',     // 换盆
      'pest_control',  // 病虫害防治
      'cleaning',      // 清洁
      'observation',   // 观察记录
      'other'          // 其他
    ];
  }

  /**
   * 频率类型枚举
   */
  static get FREQUENCY_TYPES() {
    return [
      'daily',    // 每日
      'weekly',   // 每周
      'monthly',  // 每月
      'yearly',   // 每年
      'custom'    // 自定义
    ];
  }

  /**
   * 创建提醒表（如果不存在）
   */
  static async createTable() {
    const pool = databaseManager.getPool();
    if (!pool) {
      console.log('数据库连接不可用，跳过表创建');
      return;
    }

    try {
      const createTableQuery = `
        CREATE TABLE IF NOT EXISTS reminders (
          id SERIAL PRIMARY KEY,
          plant_id INTEGER NOT NULL,
          user_id INTEGER NOT NULL,
          type VARCHAR(50) NOT NULL,
          title VARCHAR(200) NOT NULL,
          description TEXT,
          frequency VARCHAR(20) NOT NULL,
          frequency_value INTEGER DEFAULT 1,
          start_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
          next_date TIMESTAMP,
          is_active BOOLEAN DEFAULT true,
          created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
          updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        );

        CREATE INDEX IF NOT EXISTS idx_reminders_plant_id ON reminders(plant_id);
        CREATE INDEX IF NOT EXISTS idx_reminders_user_id ON reminders(user_id);
        CREATE INDEX IF NOT EXISTS idx_reminders_type ON reminders(type);
        CREATE INDEX IF NOT EXISTS idx_reminders_next_date ON reminders(next_date);
        CREATE INDEX IF NOT EXISTS idx_reminders_is_active ON reminders(is_active);
      `;

      await pool.query(createTableQuery);
      console.log('✅ 提醒表创建成功');
    } catch (error) {
      console.error('❌ 提醒表创建失败:', error.message);
    }
  }

  /**
   * 验证提醒类型
   */
  static isValidReminderType(type) {
    return Reminder.REMINDER_TYPES.includes(type);
  }

  /**
   * 验证频率类型
   */
  static isValidFrequencyType(frequency) {
    return Reminder.FREQUENCY_TYPES.includes(frequency);
  }

  /**
   * 计算下次提醒时间
   */
  static calculateNextDate(startDate, frequency, frequencyValue = 1) {
    const start = new Date(startDate);
    const next = new Date(start);

    switch (frequency) {
      case 'daily':
        next.setDate(start.getDate() + frequencyValue);
        break;
      case 'weekly':
        next.setDate(start.getDate() + (frequencyValue * 7));
        break;
      case 'monthly':
        next.setMonth(start.getMonth() + frequencyValue);
        break;
      case 'yearly':
        next.setFullYear(start.getFullYear() + frequencyValue);
        break;
      default:
        next.setDate(start.getDate() + 1); // 默认每日
    }

    return next;
  }

  /**
   * 创建新提醒
   */
  static async create(reminderData, userId) {
    const pool = databaseManager.getPool();

    // 验证提醒类型
    if (!Reminder.isValidReminderType(reminderData.type)) {
      throw new Error(`无效的提醒类型: ${reminderData.type}`);
    }

    // 验证频率类型
    if (!Reminder.isValidFrequencyType(reminderData.frequency)) {
      throw new Error(`无效的频率类型: ${reminderData.frequency}`);
    }

    // 验证植物是否存在且属于用户
    const plant = await Plant.findByIdAndUserId(reminderData.plantId, userId);
    if (!plant) {
      throw new Error('植物不存在或不属于当前用户');
    }

    // 计算下次提醒时间
    const startDate = reminderData.startDate || new Date();
    const nextDate = Reminder.calculateNextDate(startDate, reminderData.frequency, reminderData.frequencyValue);

    if (!pool) {
      // 测试环境模拟数据
      if (process.env.NODE_ENV === 'test') {
        const mockReminders = Reminder.getMockReminders();
        const newReminder = {
          id: mockReminders.length + 1,
          plant_id: reminderData.plantId,
          user_id: userId,
          type: reminderData.type,
          title: reminderData.title,
          description: reminderData.description,
          frequency: reminderData.frequency,
          frequency_value: reminderData.frequencyValue || 1,
          start_date: startDate,
          next_date: nextDate,
          is_active: reminderData.isActive !== undefined ? reminderData.isActive : true,
          created_at: new Date(),
          updated_at: new Date()
        };
        mockReminders.push(newReminder);
        return new Reminder(newReminder);
      }
      throw new Error('数据库连接不可用');
    }

    try {
      const query = `
        INSERT INTO reminders (plant_id, user_id, type, title, description, frequency, frequency_value, start_date, next_date, is_active)
        VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10)
        RETURNING id, plant_id, user_id, type, title, description, frequency, frequency_value, start_date, next_date, is_active, created_at, updated_at
      `;

      const result = await pool.query(query, [
        reminderData.plantId,
        userId,
        reminderData.type,
        reminderData.title,
        reminderData.description,
        reminderData.frequency,
        reminderData.frequencyValue || 1,
        startDate,
        nextDate,
        reminderData.isActive !== undefined ? reminderData.isActive : true
      ]);

      return new Reminder(result.rows[0]);
    } catch (error) {
      console.error('创建提醒失败:', error);
      throw error;
    }
  }

  /**
   * 根据用户ID获取提醒列表
   */
  static async findByUserId(userId, options = {}) {
    const pool = databaseManager.getPool();

    if (!pool) {
      // 测试环境模拟数据
      if (process.env.NODE_ENV === 'test') {
        const mockReminders = Reminder.getMockReminders();
        let userReminders = mockReminders.filter(reminder => reminder.user_id === parseInt(userId));

        // 应用筛选条件
        if (options.plantId) {
          userReminders = userReminders.filter(reminder =>
            reminder.plant_id === parseInt(options.plantId)
          );
        }

        if (options.type) {
          userReminders = userReminders.filter(reminder =>
            reminder.type === options.type
          );
        }

        if (options.isActive !== undefined) {
          userReminders = userReminders.filter(reminder =>
            reminder.is_active === options.isActive
          );
        }

        // 应用分页
        const page = options.page || 1;
        const limit = options.limit || 10;
        const offset = (page - 1) * limit;
        const paginatedReminders = userReminders.slice(offset, offset + limit);

        return {
          reminders: paginatedReminders.map(reminder => new Reminder(reminder)),
          total: userReminders.length,
          page: page,
          limit: limit,
          totalPages: Math.ceil(userReminders.length / limit)
        };
      }
      return { reminders: [], total: 0, page: 1, limit: 10, totalPages: 0 };
    }

    try {
      let whereClause = 'WHERE user_id = $1';
      let queryParams = [userId];
      let paramIndex = 2;

      // 添加筛选条件
      if (options.plantId) {
        whereClause += ` AND plant_id = $${paramIndex}`;
        queryParams.push(options.plantId);
        paramIndex++;
      }

      if (options.type) {
        whereClause += ` AND type = $${paramIndex}`;
        queryParams.push(options.type);
        paramIndex++;
      }

      if (options.isActive !== undefined) {
        whereClause += ` AND is_active = $${paramIndex}`;
        queryParams.push(options.isActive);
        paramIndex++;
      }

      // 计算总数
      const countQuery = `SELECT COUNT(*) FROM reminders ${whereClause}`;
      const countResult = await pool.query(countQuery, queryParams);
      const total = parseInt(countResult.rows[0].count);

      // 应用分页
      const page = options.page || 1;
      const limit = options.limit || 10;
      const offset = (page - 1) * limit;

      const query = `
        SELECT id, plant_id, user_id, type, title, description, frequency, frequency_value, start_date, next_date, is_active, created_at, updated_at
        FROM reminders ${whereClause}
        ORDER BY next_date ASC
        LIMIT $${paramIndex} OFFSET $${paramIndex + 1}
      `;

      queryParams.push(limit, offset);
      const result = await pool.query(query, queryParams);

      return {
        reminders: result.rows.map(row => new Reminder(row)),
        total,
        page,
        limit,
        totalPages: Math.ceil(total / limit)
      };
    } catch (error) {
      console.error('获取提醒列表失败:', error);
      throw error;
    }
  }

  /**
   * 根据ID和用户ID查找提醒
   */
  static async findByIdAndUserId(id, userId) {
    const pool = databaseManager.getPool();

    if (!pool) {
      // 测试环境模拟数据
      if (process.env.NODE_ENV === 'test') {
        const mockReminders = Reminder.getMockReminders();
        const reminderData = mockReminders.find(reminder =>
          reminder.id === parseInt(id) && reminder.user_id === parseInt(userId)
        );
        return reminderData ? new Reminder(reminderData) : null;
      }
      return null;
    }

    try {
      const query = 'SELECT * FROM reminders WHERE id = $1 AND user_id = $2';
      const result = await pool.query(query, [id, userId]);

      if (result.rows.length === 0) {
        return null;
      }

      return new Reminder(result.rows[0]);
    } catch (error) {
      console.error('查找提醒失败:', error);
      throw error;
    }
  }

  /**
   * 更新提醒
   */
  static async updateByIdAndUserId(id, userId, updateData) {
    const pool = databaseManager.getPool();

    // 验证提醒类型（如果提供）
    if (updateData.type && !Reminder.isValidReminderType(updateData.type)) {
      throw new Error(`无效的提醒类型: ${updateData.type}`);
    }

    // 验证频率类型（如果提供）
    if (updateData.frequency && !Reminder.isValidFrequencyType(updateData.frequency)) {
      throw new Error(`无效的频率类型: ${updateData.frequency}`);
    }

    if (!pool) {
      // 测试环境模拟数据
      if (process.env.NODE_ENV === 'test') {
        const mockReminders = Reminder.getMockReminders();
        const reminderIndex = mockReminders.findIndex(reminder =>
          reminder.id === parseInt(id) && reminder.user_id === parseInt(userId)
        );

        if (reminderIndex === -1) {
          return null;
        }

        // 更新提醒数据
        const updatedFields = {};
        Object.keys(updateData).forEach(key => {
          if (key === 'isActive') {
            updatedFields.is_active = updateData[key];
          } else if (key === 'frequencyValue') {
            updatedFields.frequency_value = updateData[key];
          } else if (key === 'startDate') {
            updatedFields.start_date = updateData[key];
          } else if (key === 'nextDate') {
            updatedFields.next_date = updateData[key];
          } else {
            updatedFields[key] = updateData[key];
          }
        });

        Object.assign(mockReminders[reminderIndex], updatedFields, { updated_at: new Date() });

        // 如果更新了频率相关信息，重新计算下次提醒时间
        if (updateData.frequency || updateData.frequencyValue || updateData.startDate) {
          const reminder = mockReminders[reminderIndex];
          reminder.next_date = Reminder.calculateNextDate(
            reminder.start_date,
            reminder.frequency,
            reminder.frequency_value
          );
        }

        return new Reminder(mockReminders[reminderIndex]);
      }
      return null;
    }

    try {
      const setClause = [];
      const values = [];
      let paramIndex = 1;

      // 构建动态更新语句
      Object.keys(updateData).forEach(key => {
        if (updateData[key] !== undefined) {
          // 处理字段名映射
          let dbFieldName = key;
          if (key === 'isActive') {
            dbFieldName = 'is_active';
          } else if (key === 'frequencyValue') {
            dbFieldName = 'frequency_value';
          } else if (key === 'startDate') {
            dbFieldName = 'start_date';
          } else if (key === 'nextDate') {
            dbFieldName = 'next_date';
          }

          setClause.push(`${dbFieldName} = $${paramIndex}`);
          values.push(updateData[key]);
          paramIndex++;
        }
      });

      if (setClause.length === 0) {
        throw new Error('没有提供更新数据');
      }

      setClause.push(`updated_at = $${paramIndex}`);
      values.push(new Date());
      values.push(id, userId);

      const query = `
        UPDATE reminders
        SET ${setClause.join(', ')}
        WHERE id = $${paramIndex + 1} AND user_id = $${paramIndex + 2}
        RETURNING *
      `;

      const result = await pool.query(query, values);

      if (result.rows.length === 0) {
        return null;
      }

      return new Reminder(result.rows[0]);
    } catch (error) {
      console.error('更新提醒失败:', error);
      throw error;
    }
  }

  /**
   * 删除提醒
   */
  static async deleteByIdAndUserId(id, userId) {
    const pool = databaseManager.getPool();

    if (!pool) {
      // 测试环境模拟数据
      if (process.env.NODE_ENV === 'test') {
        const mockReminders = Reminder.getMockReminders();
        const reminderIndex = mockReminders.findIndex(reminder =>
          reminder.id === parseInt(id) && reminder.user_id === parseInt(userId)
        );

        if (reminderIndex === -1) {
          return false;
        }

        mockReminders.splice(reminderIndex, 1);
        return true;
      }
      return false;
    }

    try {
      const query = 'DELETE FROM reminders WHERE id = $1 AND user_id = $2';
      const result = await pool.query(query, [id, userId]);

      return result.rowCount > 0;
    } catch (error) {
      console.error('删除提醒失败:', error);
      throw error;
    }
  }

  /**
   * 获取今日提醒
   */
  static async getTodayReminders(userId) {
    const today = new Date();
    const startOfDay = new Date(today.getFullYear(), today.getMonth(), today.getDate());
    const endOfDay = new Date(today.getFullYear(), today.getMonth(), today.getDate() + 1);

    const pool = databaseManager.getPool();

    if (!pool) {
      // 测试环境模拟数据
      if (process.env.NODE_ENV === 'test') {
        const mockReminders = Reminder.getMockReminders();
        const todayReminders = mockReminders.filter(reminder =>
          reminder.user_id === parseInt(userId) &&
          reminder.is_active &&
          new Date(reminder.next_date) >= startOfDay &&
          new Date(reminder.next_date) < endOfDay
        );

        return todayReminders.map(reminder => new Reminder(reminder));
      }
      return [];
    }

    try {
      const query = `
        SELECT * FROM reminders
        WHERE user_id = $1 AND is_active = true
        AND next_date >= $2 AND next_date < $3
        ORDER BY next_date ASC
      `;

      const result = await pool.query(query, [userId, startOfDay, endOfDay]);
      return result.rows.map(row => new Reminder(row));
    } catch (error) {
      console.error('获取今日提醒失败:', error);
      throw error;
    }
  }

  /**
   * 批量创建提醒
   */
  static async createBatch(plantId, reminders, userId) {
    const createdReminders = [];

    for (const reminderData of reminders) {
      const fullReminderData = {
        ...reminderData,
        plantId: plantId
      };

      const reminder = await Reminder.create(fullReminderData, userId);
      createdReminders.push(reminder);
    }

    return createdReminders;
  }

  /**
   * 转换为JSON对象
   */
  toJSON() {
    return {
      id: this.id,
      plantId: this.plantId,
      userId: this.userId,
      type: this.type,
      title: this.title,
      description: this.description,
      frequency: this.frequency,
      frequencyValue: this.frequencyValue,
      startDate: this.startDate,
      nextDate: this.nextDate,
      isActive: this.isActive,
      createdAt: this.createdAt,
      updatedAt: this.updatedAt
    };
  }

  /**
   * 获取模拟提醒数据（用于测试）
   */
  static getMockReminders() {
    if (!Reminder._mockReminders) {
      Reminder._mockReminders = [];
    }
    return Reminder._mockReminders;
  }

  /**
   * 清除模拟提醒数据（用于测试）
   */
  static clearMockReminders() {
    Reminder._mockReminders = [];
  }
}

module.exports = Reminder;
