/**
 * 植物路由
 * 处理植物相关的API端点
 */

const express = require('express');
const router = express.Router();

const PlantController = require('../controllers/plantController');
const { authenticateToken } = require('../middlewares/auth');
const { 
  validate, 
  plantCreateValidation, 
  plantUpdateValidation 
} = require('../middlewares/validation');

/**
 * 获取植物统计信息
 * GET /api/v1/plants/stats
 */
router.get('/stats', 
  authenticateToken,
  PlantController.getPlantStats
);

/**
 * 创建植物
 * POST /api/v1/plants
 */
router.post('/', 
  authenticateToken,
  validate(plantCreateValidation),
  PlantController.createPlant
);

/**
 * 获取植物列表
 * GET /api/v1/plants
 */
router.get('/', 
  authenticateToken,
  PlantController.getPlants
);

/**
 * 获取单个植物详情
 * GET /api/v1/plants/:id
 */
router.get('/:id', 
  authenticateToken,
  PlantController.getPlantById
);

/**
 * 更新植物信息
 * PUT /api/v1/plants/:id
 */
router.put('/:id', 
  authenticateToken,
  validate(plantUpdateValidation),
  PlantController.updatePlant
);

/**
 * 删除植物
 * DELETE /api/v1/plants/:id
 */
router.delete('/:id', 
  authenticateToken,
  PlantController.deletePlant
);

module.exports = router;
