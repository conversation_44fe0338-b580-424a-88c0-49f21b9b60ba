/**
 * JWT工具类
 * 处理JWT令牌的生成和验证
 */

const jwt = require('jsonwebtoken');
const config = require('../config');

class JWTUtils {
  /**
   * 生成访问令牌
   */
  static generateAccessToken(payload) {
    return jwt.sign(
      payload,
      config.jwt.secret,
      { 
        expiresIn: config.jwt.expiresIn,
        issuer: 'plantapp-backend',
        audience: 'plantapp-client'
      }
    );
  }

  /**
   * 生成刷新令牌
   */
  static generateRefreshToken(payload) {
    return jwt.sign(
      payload,
      config.jwt.refreshSecret,
      { 
        expiresIn: config.jwt.refreshExpiresIn,
        issuer: 'plantapp-backend',
        audience: 'plantapp-client'
      }
    );
  }

  /**
   * 验证访问令牌
   */
  static verifyAccessToken(token) {
    try {
      return jwt.verify(token, config.jwt.secret, {
        issuer: 'plantapp-backend',
        audience: 'plantapp-client'
      });
    } catch (error) {
      throw new Error('无效的认证令牌');
    }
  }

  /**
   * 验证刷新令牌
   */
  static verifyRefreshToken(token) {
    try {
      return jwt.verify(token, config.jwt.refreshSecret, {
        issuer: 'plantapp-backend',
        audience: 'plantapp-client'
      });
    } catch (error) {
      throw new Error('无效的刷新令牌');
    }
  }

  /**
   * 从请求头中提取令牌
   */
  static extractTokenFromHeader(authHeader) {
    if (!authHeader) {
      throw new Error('未提供认证令牌');
    }

    const parts = authHeader.split(' ');
    if (parts.length !== 2 || parts[0] !== 'Bearer') {
      throw new Error('认证令牌格式错误');
    }

    return parts[1];
  }

  /**
   * 生成令牌对（访问令牌和刷新令牌）
   */
  static generateTokenPair(user) {
    const payload = {
      userId: user.id,
      email: user.email,
      username: user.username
    };

    const accessToken = this.generateAccessToken(payload);
    const refreshToken = this.generateRefreshToken({ userId: user.id });

    return {
      token: accessToken,
      refreshToken: refreshToken,
      expiresIn: config.jwt.expiresIn
    };
  }
}

module.exports = JWTUtils;
