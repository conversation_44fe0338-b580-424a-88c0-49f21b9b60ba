/**
 * 提醒路由
 * 处理提醒相关的API端点
 */

const express = require('express');
const router = express.Router();

const ReminderController = require('../controllers/reminderController');
const { authenticateToken } = require('../middlewares/auth');
const { 
  validate, 
  reminderCreateValidation, 
  reminderUpdateValidation,
  reminderStatusValidation,
  batchReminderValidation
} = require('../middlewares/validation');

/**
 * 获取今日提醒
 * GET /api/v1/reminders/today
 */
router.get('/today', 
  authenticateToken,
  ReminderController.getTodayReminders
);

/**
 * 批量创建提醒
 * POST /api/v1/reminders/batch
 */
router.post('/batch', 
  authenticateToken,
  validate(batchReminderValidation),
  ReminderController.createBatchReminders
);

/**
 * 创建提醒
 * POST /api/v1/reminders
 */
router.post('/', 
  authenticateToken,
  validate(reminderCreateValidation),
  ReminderController.createReminder
);

/**
 * 获取提醒列表
 * GET /api/v1/reminders
 */
router.get('/', 
  authenticateToken,
  ReminderController.getReminders
);

/**
 * 获取单个提醒详情
 * GET /api/v1/reminders/:id
 */
router.get('/:id', 
  authenticateToken,
  ReminderController.getReminderById
);

/**
 * 更新提醒状态
 * PUT /api/v1/reminders/:id/status
 */
router.put('/:id/status', 
  authenticateToken,
  validate(reminderStatusValidation),
  ReminderController.updateReminderStatus
);

/**
 * 更新提醒
 * PUT /api/v1/reminders/:id
 */
router.put('/:id', 
  authenticateToken,
  validate(reminderUpdateValidation),
  ReminderController.updateReminder
);

/**
 * 删除提醒
 * DELETE /api/v1/reminders/:id
 */
router.delete('/:id', 
  authenticateToken,
  ReminderController.deleteReminder
);

module.exports = router;
