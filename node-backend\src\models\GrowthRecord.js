/**
 * 生长记录模型
 * 处理植物生长记录数据的CRUD操作
 */

const databaseManager = require('../config/database');
const Plant = require('./Plant');

class GrowthRecord {
  constructor(recordData) {
    this.id = recordData.id;
    this.plantId = recordData.plant_id || recordData.plantId;
    this.userId = recordData.user_id || recordData.userId;
    this.height = recordData.height;
    this.width = recordData.width;
    this.leafCount = recordData.leaf_count || recordData.leafCount;
    this.notes = recordData.notes;
    this.images = recordData.images ? (Array.isArray(recordData.images) ? recordData.images : JSON.parse(recordData.images)) : [];
    this.recordDate = recordData.record_date || recordData.recordDate;
    this.createdAt = recordData.created_at || recordData.createdAt;
    this.updatedAt = recordData.updated_at || recordData.updatedAt;
  }

  /**
   * 创建生长记录表（如果不存在）
   */
  static async createTable() {
    const pool = databaseManager.getPool();
    if (!pool) {
      console.log('数据库连接不可用，跳过表创建');
      return;
    }

    try {
      const createTableQuery = `
        CREATE TABLE IF NOT EXISTS growth_records (
          id SERIAL PRIMARY KEY,
          plant_id INTEGER NOT NULL,
          user_id INTEGER NOT NULL,
          height DECIMAL(10,2),
          width DECIMAL(10,2),
          leaf_count INTEGER,
          notes TEXT,
          images JSONB,
          record_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
          created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
          updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        );
        
        CREATE INDEX IF NOT EXISTS idx_growth_records_plant_id ON growth_records(plant_id);
        CREATE INDEX IF NOT EXISTS idx_growth_records_user_id ON growth_records(user_id);
        CREATE INDEX IF NOT EXISTS idx_growth_records_record_date ON growth_records(record_date);
      `;

      await pool.query(createTableQuery);
      console.log('✅ 生长记录表创建成功');
    } catch (error) {
      console.error('❌ 生长记录表创建失败:', error.message);
    }
  }

  /**
   * 创建新生长记录
   */
  static async create(recordData, userId) {
    const pool = databaseManager.getPool();

    // 验证植物是否存在且属于用户
    const plant = await Plant.findByIdAndUserId(recordData.plantId, userId);
    if (!plant) {
      throw new Error('植物不存在或不属于当前用户');
    }

    if (!pool) {
      // 测试环境模拟数据
      if (process.env.NODE_ENV === 'test') {
        const mockRecords = GrowthRecord.getMockRecords();
        const newRecord = {
          id: mockRecords.length + 1,
          plant_id: recordData.plantId,
          user_id: userId,
          height: recordData.height,
          width: recordData.width,
          leaf_count: recordData.leafCount,
          notes: recordData.notes,
          images: recordData.images || [],
          record_date: recordData.recordDate || new Date(),
          created_at: new Date(),
          updated_at: new Date()
        };
        mockRecords.push(newRecord);
        return new GrowthRecord(newRecord);
      }
      throw new Error('数据库连接不可用');
    }

    try {
      const query = `
        INSERT INTO growth_records (plant_id, user_id, height, width, leaf_count, notes, images, record_date)
        VALUES ($1, $2, $3, $4, $5, $6, $7, $8)
        RETURNING id, plant_id, user_id, height, width, leaf_count, notes, images, record_date, created_at, updated_at
      `;
      
      const result = await pool.query(query, [
        recordData.plantId,
        userId,
        recordData.height,
        recordData.width,
        recordData.leafCount,
        recordData.notes,
        JSON.stringify(recordData.images || []),
        recordData.recordDate || new Date()
      ]);

      return new GrowthRecord(result.rows[0]);
    } catch (error) {
      console.error('创建生长记录失败:', error);
      throw error;
    }
  }

  /**
   * 根据用户ID获取生长记录列表
   */
  static async findByUserId(userId, options = {}) {
    const pool = databaseManager.getPool();
    
    if (!pool) {
      // 测试环境模拟数据
      if (process.env.NODE_ENV === 'test') {
        const mockRecords = GrowthRecord.getMockRecords();
        let userRecords = mockRecords.filter(record => record.user_id === parseInt(userId));
        
        // 应用筛选条件
        if (options.plantId) {
          userRecords = userRecords.filter(record => 
            record.plant_id === parseInt(options.plantId)
          );
        }
        
        if (options.startDate || options.endDate) {
          userRecords = userRecords.filter(record => {
            const recordDate = new Date(record.record_date);
            if (options.startDate && recordDate < new Date(options.startDate)) {
              return false;
            }
            if (options.endDate && recordDate > new Date(options.endDate)) {
              return false;
            }
            return true;
          });
        }
        
        // 应用分页
        const page = options.page || 1;
        const limit = options.limit || 10;
        const offset = (page - 1) * limit;
        const paginatedRecords = userRecords.slice(offset, offset + limit);
        
        return {
          growthRecords: paginatedRecords.map(record => new GrowthRecord(record)),
          total: userRecords.length,
          page: page,
          limit: limit,
          totalPages: Math.ceil(userRecords.length / limit)
        };
      }
      return { growthRecords: [], total: 0, page: 1, limit: 10, totalPages: 0 };
    }

    try {
      let whereClause = 'WHERE user_id = $1';
      let queryParams = [userId];
      let paramIndex = 2;

      // 添加筛选条件
      if (options.plantId) {
        whereClause += ` AND plant_id = $${paramIndex}`;
        queryParams.push(options.plantId);
        paramIndex++;
      }

      if (options.startDate) {
        whereClause += ` AND record_date >= $${paramIndex}`;
        queryParams.push(options.startDate);
        paramIndex++;
      }

      if (options.endDate) {
        whereClause += ` AND record_date <= $${paramIndex}`;
        queryParams.push(options.endDate);
        paramIndex++;
      }

      // 计算总数
      const countQuery = `SELECT COUNT(*) FROM growth_records ${whereClause}`;
      const countResult = await pool.query(countQuery, queryParams);
      const total = parseInt(countResult.rows[0].count);

      // 应用分页
      const page = options.page || 1;
      const limit = options.limit || 10;
      const offset = (page - 1) * limit;

      const query = `
        SELECT id, plant_id, user_id, height, width, leaf_count, notes, images, record_date, created_at, updated_at
        FROM growth_records ${whereClause}
        ORDER BY record_date DESC
        LIMIT $${paramIndex} OFFSET $${paramIndex + 1}
      `;
      
      queryParams.push(limit, offset);
      const result = await pool.query(query, queryParams);

      return {
        growthRecords: result.rows.map(row => new GrowthRecord(row)),
        total,
        page,
        limit,
        totalPages: Math.ceil(total / limit)
      };
    } catch (error) {
      console.error('获取生长记录列表失败:', error);
      throw error;
    }
  }

  /**
   * 根据ID和用户ID查找生长记录
   */
  static async findByIdAndUserId(id, userId) {
    const pool = databaseManager.getPool();
    
    if (!pool) {
      // 测试环境模拟数据
      if (process.env.NODE_ENV === 'test') {
        const mockRecords = GrowthRecord.getMockRecords();
        const recordData = mockRecords.find(record => 
          record.id === parseInt(id) && record.user_id === parseInt(userId)
        );
        return recordData ? new GrowthRecord(recordData) : null;
      }
      return null;
    }

    try {
      const query = 'SELECT * FROM growth_records WHERE id = $1 AND user_id = $2';
      const result = await pool.query(query, [id, userId]);
      
      if (result.rows.length === 0) {
        return null;
      }

      return new GrowthRecord(result.rows[0]);
    } catch (error) {
      console.error('查找生长记录失败:', error);
      throw error;
    }
  }

  /**
   * 更新生长记录
   */
  static async updateByIdAndUserId(id, userId, updateData) {
    const pool = databaseManager.getPool();
    
    if (!pool) {
      // 测试环境模拟数据
      if (process.env.NODE_ENV === 'test') {
        const mockRecords = GrowthRecord.getMockRecords();
        const recordIndex = mockRecords.findIndex(record => 
          record.id === parseInt(id) && record.user_id === parseInt(userId)
        );
        
        if (recordIndex === -1) {
          return null;
        }
        
        // 更新生长记录数据
        Object.assign(mockRecords[recordIndex], updateData, { updated_at: new Date() });
        return new GrowthRecord(mockRecords[recordIndex]);
      }
      return null;
    }

    try {
      const setClause = [];
      const values = [];
      let paramIndex = 1;

      // 构建动态更新语句
      Object.keys(updateData).forEach(key => {
        if (updateData[key] !== undefined) {
          if (key === 'images') {
            setClause.push(`${key} = $${paramIndex}`);
            values.push(JSON.stringify(updateData[key]));
          } else {
            setClause.push(`${key} = $${paramIndex}`);
            values.push(updateData[key]);
          }
          paramIndex++;
        }
      });

      if (setClause.length === 0) {
        throw new Error('没有提供更新数据');
      }

      setClause.push(`updated_at = $${paramIndex}`);
      values.push(new Date());
      values.push(id, userId);

      const query = `
        UPDATE growth_records 
        SET ${setClause.join(', ')}
        WHERE id = $${paramIndex + 1} AND user_id = $${paramIndex + 2}
        RETURNING *
      `;

      const result = await pool.query(query, values);
      
      if (result.rows.length === 0) {
        return null;
      }

      return new GrowthRecord(result.rows[0]);
    } catch (error) {
      console.error('更新生长记录失败:', error);
      throw error;
    }
  }

  /**
   * 删除生长记录
   */
  static async deleteByIdAndUserId(id, userId) {
    const pool = databaseManager.getPool();
    
    if (!pool) {
      // 测试环境模拟数据
      if (process.env.NODE_ENV === 'test') {
        const mockRecords = GrowthRecord.getMockRecords();
        const recordIndex = mockRecords.findIndex(record => 
          record.id === parseInt(id) && record.user_id === parseInt(userId)
        );
        
        if (recordIndex === -1) {
          return false;
        }
        
        mockRecords.splice(recordIndex, 1);
        return true;
      }
      return false;
    }

    try {
      const query = 'DELETE FROM growth_records WHERE id = $1 AND user_id = $2';
      const result = await pool.query(query, [id, userId]);
      
      return result.rowCount > 0;
    } catch (error) {
      console.error('删除生长记录失败:', error);
      throw error;
    }
  }

  /**
   * 转换为JSON对象
   */
  toJSON() {
    return {
      id: this.id,
      plantId: this.plantId,
      userId: this.userId,
      height: this.height,
      width: this.width,
      leafCount: this.leafCount,
      notes: this.notes,
      images: this.images,
      recordDate: this.recordDate,
      createdAt: this.createdAt,
      updatedAt: this.updatedAt
    };
  }

  /**
   * 获取模拟生长记录数据（用于测试）
   */
  static getMockRecords() {
    if (!GrowthRecord._mockRecords) {
      GrowthRecord._mockRecords = [];
    }
    return GrowthRecord._mockRecords;
  }

  /**
   * 清除模拟生长记录数据（用于测试）
   */
  static clearMockRecords() {
    GrowthRecord._mockRecords = [];
  }
}

module.exports = GrowthRecord;
