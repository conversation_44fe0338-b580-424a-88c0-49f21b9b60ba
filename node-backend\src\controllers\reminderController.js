/**
 * 提醒控制器
 * 处理提醒相关的API请求
 */

const Reminder = require('../models/Reminder');
const { logger } = require('../middlewares/errorHandler');

class ReminderController {
  /**
   * 创建提醒
   * POST /api/v1/reminders
   */
  static async createReminder(req, res) {
    try {
      const userId = req.user.id;
      const reminderData = req.body;

      const newReminder = await Reminder.create(reminderData, userId);

      logger.info(`用户 ${userId} 创建了新提醒: ${newReminder.type} for plant ${newReminder.plantId}`);

      res.status(201).json({
        success: true,
        message: '提醒创建成功',
        data: {
          reminder: newReminder.toJSON()
        }
      });
    } catch (error) {
      logger.error('创建提醒失败:', error);
      
      if (error.message.includes('植物不存在') || 
          error.message.includes('无效的提醒类型') || 
          error.message.includes('无效的频率类型')) {
        return res.status(400).json({
          error: {
            message: error.message,
            status: 400
          }
        });
      }
      
      res.status(500).json({
        error: {
          message: '创建提醒失败，请稍后重试',
          status: 500
        }
      });
    }
  }

  /**
   * 获取提醒列表
   * GET /api/v1/reminders
   */
  static async getReminders(req, res) {
    try {
      const userId = req.user.id;
      const options = {
        page: parseInt(req.query.page) || 1,
        limit: parseInt(req.query.limit) || 10,
        plantId: req.query.plantId ? parseInt(req.query.plantId) : undefined,
        type: req.query.type,
        isActive: req.query.isActive !== undefined ? req.query.isActive === 'true' : undefined
      };

      // 验证分页参数
      if (options.page < 1) options.page = 1;
      if (options.limit < 1 || options.limit > 100) options.limit = 10;

      // 验证提醒类型
      if (options.type && !Reminder.isValidReminderType(options.type)) {
        return res.status(400).json({
          error: {
            message: '无效的提醒类型',
            status: 400
          }
        });
      }

      const result = await Reminder.findByUserId(userId, options);

      res.json({
        success: true,
        message: '获取提醒列表成功',
        data: {
          reminders: result.reminders.map(reminder => reminder.toJSON()),
          pagination: {
            page: result.page,
            limit: result.limit,
            total: result.total,
            totalPages: result.totalPages
          }
        }
      });
    } catch (error) {
      logger.error('获取提醒列表失败:', error);
      
      res.status(500).json({
        error: {
          message: '获取提醒列表失败，请稍后重试',
          status: 500
        }
      });
    }
  }

  /**
   * 获取单个提醒详情
   * GET /api/v1/reminders/:id
   */
  static async getReminderById(req, res) {
    try {
      const userId = req.user.id;
      const reminderId = req.params.id;

      // 验证提醒ID
      if (!reminderId || isNaN(reminderId)) {
        return res.status(400).json({
          error: {
            message: '无效的提醒ID',
            status: 400
          }
        });
      }

      const reminder = await Reminder.findByIdAndUserId(reminderId, userId);

      if (!reminder) {
        return res.status(404).json({
          error: {
            message: '提醒不存在或不属于当前用户',
            status: 404
          }
        });
      }

      res.json({
        success: true,
        message: '获取提醒详情成功',
        data: {
          reminder: reminder.toJSON()
        }
      });
    } catch (error) {
      logger.error('获取提醒详情失败:', error);
      
      res.status(500).json({
        error: {
          message: '获取提醒详情失败，请稍后重试',
          status: 500
        }
      });
    }
  }

  /**
   * 更新提醒
   * PUT /api/v1/reminders/:id
   */
  static async updateReminder(req, res) {
    try {
      const userId = req.user.id;
      const reminderId = req.params.id;
      const updateData = req.body;

      // 验证提醒ID
      if (!reminderId || isNaN(reminderId)) {
        return res.status(400).json({
          error: {
            message: '无效的提醒ID',
            status: 400
          }
        });
      }

      // 检查提醒是否存在
      const existingReminder = await Reminder.findByIdAndUserId(reminderId, userId);
      if (!existingReminder) {
        return res.status(404).json({
          error: {
            message: '提醒不存在或不属于当前用户',
            status: 404
          }
        });
      }

      const updatedReminder = await Reminder.updateByIdAndUserId(reminderId, userId, updateData);

      if (!updatedReminder) {
        return res.status(404).json({
          error: {
            message: '更新失败，提醒不存在',
            status: 404
          }
        });
      }

      logger.info(`用户 ${userId} 更新了提醒 ${reminderId}`);

      res.json({
        success: true,
        message: '提醒更新成功',
        data: {
          reminder: updatedReminder.toJSON()
        }
      });
    } catch (error) {
      logger.error('更新提醒失败:', error);
      
      if (error.message.includes('无效的提醒类型') || 
          error.message.includes('无效的频率类型')) {
        return res.status(400).json({
          error: {
            message: error.message,
            status: 400
          }
        });
      }
      
      res.status(500).json({
        error: {
          message: '更新提醒失败，请稍后重试',
          status: 500
        }
      });
    }
  }

  /**
   * 更新提醒状态
   * PUT /api/v1/reminders/:id/status
   */
  static async updateReminderStatus(req, res) {
    try {
      const userId = req.user.id;
      const reminderId = req.params.id;
      const { isActive } = req.body;

      // 验证提醒ID
      if (!reminderId || isNaN(reminderId)) {
        return res.status(400).json({
          error: {
            message: '无效的提醒ID',
            status: 400
          }
        });
      }

      // 检查提醒是否存在
      const existingReminder = await Reminder.findByIdAndUserId(reminderId, userId);
      if (!existingReminder) {
        return res.status(404).json({
          error: {
            message: '提醒不存在或不属于当前用户',
            status: 404
          }
        });
      }

      const updatedReminder = await Reminder.updateByIdAndUserId(reminderId, userId, { isActive });

      if (!updatedReminder) {
        return res.status(404).json({
          error: {
            message: '更新失败，提醒不存在',
            status: 404
          }
        });
      }

      logger.info(`用户 ${userId} 更新了提醒 ${reminderId} 状态为 ${isActive}`);

      res.json({
        success: true,
        message: '提醒状态更新成功',
        data: {
          reminder: updatedReminder.toJSON()
        }
      });
    } catch (error) {
      logger.error('更新提醒状态失败:', error);
      
      res.status(500).json({
        error: {
          message: '更新提醒状态失败，请稍后重试',
          status: 500
        }
      });
    }
  }

  /**
   * 删除提醒
   * DELETE /api/v1/reminders/:id
   */
  static async deleteReminder(req, res) {
    try {
      const userId = req.user.id;
      const reminderId = req.params.id;

      // 验证提醒ID
      if (!reminderId || isNaN(reminderId)) {
        return res.status(400).json({
          error: {
            message: '无效的提醒ID',
            status: 400
          }
        });
      }

      // 检查提醒是否存在
      const existingReminder = await Reminder.findByIdAndUserId(reminderId, userId);
      if (!existingReminder) {
        return res.status(404).json({
          error: {
            message: '提醒不存在或不属于当前用户',
            status: 404
          }
        });
      }

      const deleted = await Reminder.deleteByIdAndUserId(reminderId, userId);

      if (!deleted) {
        return res.status(404).json({
          error: {
            message: '删除失败，提醒不存在',
            status: 404
          }
        });
      }

      logger.info(`用户 ${userId} 删除了提醒 ${reminderId}`);

      res.json({
        success: true,
        message: '提醒删除成功'
      });
    } catch (error) {
      logger.error('删除提醒失败:', error);
      
      res.status(500).json({
        error: {
          message: '删除提醒失败，请稍后重试',
          status: 500
        }
      });
    }
  }

  /**
   * 获取今日提醒
   * GET /api/v1/reminders/today
   */
  static async getTodayReminders(req, res) {
    try {
      const userId = req.user.id;

      const todayReminders = await Reminder.getTodayReminders(userId);

      res.json({
        success: true,
        message: '获取今日提醒成功',
        data: {
          reminders: todayReminders.map(reminder => reminder.toJSON())
        }
      });
    } catch (error) {
      logger.error('获取今日提醒失败:', error);
      
      res.status(500).json({
        error: {
          message: '获取今日提醒失败，请稍后重试',
          status: 500
        }
      });
    }
  }

  /**
   * 批量创建提醒
   * POST /api/v1/reminders/batch
   */
  static async createBatchReminders(req, res) {
    try {
      const userId = req.user.id;
      const { plantId, reminders } = req.body;

      const createdReminders = await Reminder.createBatch(plantId, reminders, userId);

      logger.info(`用户 ${userId} 批量创建了 ${createdReminders.length} 个提醒 for plant ${plantId}`);

      res.status(201).json({
        success: true,
        message: '批量提醒创建成功',
        data: {
          reminders: createdReminders.map(reminder => reminder.toJSON())
        }
      });
    } catch (error) {
      logger.error('批量创建提醒失败:', error);
      
      if (error.message.includes('植物不存在') || 
          error.message.includes('无效的提醒类型') || 
          error.message.includes('无效的频率类型')) {
        return res.status(400).json({
          error: {
            message: error.message,
            status: 400
          }
        });
      }
      
      res.status(500).json({
        error: {
          message: '批量创建提醒失败，请稍后重试',
          status: 500
        }
      });
    }
  }
}

module.exports = ReminderController;
