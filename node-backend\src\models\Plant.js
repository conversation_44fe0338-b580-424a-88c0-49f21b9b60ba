/**
 * 植物模型
 * 处理植物数据的CRUD操作
 */

const databaseManager = require('../config/database');

class Plant {
  constructor(plantData) {
    this.id = plantData.id;
    this.name = plantData.name;
    this.species = plantData.species;
    this.description = plantData.description;
    this.location = plantData.location;
    this.acquiredDate = plantData.acquired_date || plantData.acquiredDate;
    this.image = plantData.image;
    this.userId = plantData.user_id || plantData.userId;
    this.createdAt = plantData.created_at || plantData.createdAt;
    this.updatedAt = plantData.updated_at || plantData.updatedAt;
  }

  /**
   * 创建植物表（如果不存在）
   */
  static async createTable() {
    const pool = databaseManager.getPool();
    if (!pool) {
      console.log('数据库连接不可用，跳过表创建');
      return;
    }

    try {
      const createTableQuery = `
        CREATE TABLE IF NOT EXISTS plants (
          id SERIAL PRIMARY KEY,
          name VARCHAR(100) NOT NULL,
          species VARCHAR(200),
          description TEXT,
          location VARCHAR(100),
          acquired_date DATE,
          image TEXT,
          user_id INTEGER NOT NULL,
          created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
          updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        );
        
        CREATE INDEX IF NOT EXISTS idx_plants_user_id ON plants(user_id);
        CREATE INDEX IF NOT EXISTS idx_plants_name ON plants(name);
        CREATE INDEX IF NOT EXISTS idx_plants_species ON plants(species);
      `;

      await pool.query(createTableQuery);
      console.log('✅ 植物表创建成功');
    } catch (error) {
      console.error('❌ 植物表创建失败:', error.message);
    }
  }

  /**
   * 创建新植物
   */
  static async create(plantData, userId) {
    const pool = databaseManager.getPool();

    if (!pool) {
      // 测试环境模拟数据
      if (process.env.NODE_ENV === 'test') {
        const mockPlants = Plant.getMockPlants();
        const newPlant = {
          id: mockPlants.length + 1,
          name: plantData.name,
          species: plantData.species,
          description: plantData.description,
          location: plantData.location,
          acquired_date: plantData.acquiredDate,
          image: plantData.image,
          user_id: userId,
          created_at: new Date(),
          updated_at: new Date()
        };
        mockPlants.push(newPlant);
        return new Plant(newPlant);
      }
      throw new Error('数据库连接不可用');
    }

    try {
      const query = `
        INSERT INTO plants (name, species, description, location, acquired_date, image, user_id)
        VALUES ($1, $2, $3, $4, $5, $6, $7)
        RETURNING id, name, species, description, location, acquired_date, image, user_id, created_at, updated_at
      `;
      
      const result = await pool.query(query, [
        plantData.name,
        plantData.species,
        plantData.description,
        plantData.location,
        plantData.acquiredDate,
        plantData.image,
        userId
      ]);

      return new Plant(result.rows[0]);
    } catch (error) {
      console.error('创建植物失败:', error);
      throw error;
    }
  }

  /**
   * 根据用户ID获取植物列表
   */
  static async findByUserId(userId, options = {}) {
    const pool = databaseManager.getPool();
    
    if (!pool) {
      // 测试环境模拟数据
      if (process.env.NODE_ENV === 'test') {
        const mockPlants = Plant.getMockPlants();
        let userPlants = mockPlants.filter(plant => plant.user_id === parseInt(userId));
        
        // 应用搜索过滤
        if (options.search) {
          userPlants = userPlants.filter(plant => 
            plant.name.toLowerCase().includes(options.search.toLowerCase()) ||
            (plant.species && plant.species.toLowerCase().includes(options.search.toLowerCase()))
          );
        }
        
        // 应用分页
        const page = options.page || 1;
        const limit = options.limit || 10;
        const offset = (page - 1) * limit;
        const paginatedPlants = userPlants.slice(offset, offset + limit);
        
        return {
          plants: paginatedPlants.map(plant => new Plant(plant)),
          total: userPlants.length,
          page: page,
          limit: limit,
          totalPages: Math.ceil(userPlants.length / limit)
        };
      }
      return { plants: [], total: 0, page: 1, limit: 10, totalPages: 0 };
    }

    try {
      let whereClause = 'WHERE user_id = $1';
      let queryParams = [userId];
      let paramIndex = 2;

      // 添加搜索条件
      if (options.search) {
        whereClause += ` AND (name ILIKE $${paramIndex} OR species ILIKE $${paramIndex})`;
        queryParams.push(`%${options.search}%`);
        paramIndex++;
      }

      // 计算总数
      const countQuery = `SELECT COUNT(*) FROM plants ${whereClause}`;
      const countResult = await pool.query(countQuery, queryParams);
      const total = parseInt(countResult.rows[0].count);

      // 应用分页
      const page = options.page || 1;
      const limit = options.limit || 10;
      const offset = (page - 1) * limit;

      const query = `
        SELECT id, name, species, description, location, acquired_date, image, user_id, created_at, updated_at
        FROM plants ${whereClause}
        ORDER BY created_at DESC
        LIMIT $${paramIndex} OFFSET $${paramIndex + 1}
      `;
      
      queryParams.push(limit, offset);
      const result = await pool.query(query, queryParams);

      return {
        plants: result.rows.map(row => new Plant(row)),
        total,
        page,
        limit,
        totalPages: Math.ceil(total / limit)
      };
    } catch (error) {
      console.error('获取植物列表失败:', error);
      throw error;
    }
  }

  /**
   * 根据ID和用户ID查找植物
   */
  static async findByIdAndUserId(id, userId) {
    const pool = databaseManager.getPool();
    
    if (!pool) {
      // 测试环境模拟数据
      if (process.env.NODE_ENV === 'test') {
        const mockPlants = Plant.getMockPlants();
        const plantData = mockPlants.find(plant => 
          plant.id === parseInt(id) && plant.user_id === parseInt(userId)
        );
        return plantData ? new Plant(plantData) : null;
      }
      return null;
    }

    try {
      const query = 'SELECT * FROM plants WHERE id = $1 AND user_id = $2';
      const result = await pool.query(query, [id, userId]);
      
      if (result.rows.length === 0) {
        return null;
      }

      return new Plant(result.rows[0]);
    } catch (error) {
      console.error('查找植物失败:', error);
      throw error;
    }
  }

  /**
   * 更新植物信息
   */
  static async updateByIdAndUserId(id, userId, updateData) {
    const pool = databaseManager.getPool();
    
    if (!pool) {
      // 测试环境模拟数据
      if (process.env.NODE_ENV === 'test') {
        const mockPlants = Plant.getMockPlants();
        const plantIndex = mockPlants.findIndex(plant => 
          plant.id === parseInt(id) && plant.user_id === parseInt(userId)
        );
        
        if (plantIndex === -1) {
          return null;
        }
        
        // 更新植物数据
        Object.assign(mockPlants[plantIndex], updateData, { updated_at: new Date() });
        return new Plant(mockPlants[plantIndex]);
      }
      return null;
    }

    try {
      const setClause = [];
      const values = [];
      let paramIndex = 1;

      // 构建动态更新语句
      Object.keys(updateData).forEach(key => {
        if (updateData[key] !== undefined) {
          setClause.push(`${key} = $${paramIndex}`);
          values.push(updateData[key]);
          paramIndex++;
        }
      });

      if (setClause.length === 0) {
        throw new Error('没有提供更新数据');
      }

      setClause.push(`updated_at = $${paramIndex}`);
      values.push(new Date());
      values.push(id, userId);

      const query = `
        UPDATE plants 
        SET ${setClause.join(', ')}
        WHERE id = $${paramIndex + 1} AND user_id = $${paramIndex + 2}
        RETURNING *
      `;

      const result = await pool.query(query, values);
      
      if (result.rows.length === 0) {
        return null;
      }

      return new Plant(result.rows[0]);
    } catch (error) {
      console.error('更新植物失败:', error);
      throw error;
    }
  }

  /**
   * 删除植物
   */
  static async deleteByIdAndUserId(id, userId) {
    const pool = databaseManager.getPool();
    
    if (!pool) {
      // 测试环境模拟数据
      if (process.env.NODE_ENV === 'test') {
        const mockPlants = Plant.getMockPlants();
        const plantIndex = mockPlants.findIndex(plant => 
          plant.id === parseInt(id) && plant.user_id === parseInt(userId)
        );
        
        if (plantIndex === -1) {
          return false;
        }
        
        mockPlants.splice(plantIndex, 1);
        return true;
      }
      return false;
    }

    try {
      const query = 'DELETE FROM plants WHERE id = $1 AND user_id = $2';
      const result = await pool.query(query, [id, userId]);
      
      return result.rowCount > 0;
    } catch (error) {
      console.error('删除植物失败:', error);
      throw error;
    }
  }

  /**
   * 转换为安全的JSON对象
   */
  toJSON() {
    return {
      id: this.id,
      name: this.name,
      species: this.species,
      description: this.description,
      location: this.location,
      acquiredDate: this.acquiredDate,
      image: this.image,
      userId: this.userId,
      createdAt: this.createdAt,
      updatedAt: this.updatedAt
    };
  }

  /**
   * 获取模拟植物数据（用于测试）
   */
  static getMockPlants() {
    if (!Plant._mockPlants) {
      Plant._mockPlants = [];
    }
    return Plant._mockPlants;
  }

  /**
   * 清除模拟植物数据（用于测试）
   */
  static clearMockPlants() {
    Plant._mockPlants = [];
  }
}

module.exports = Plant;
