/**
 * 生长记录控制器
 * 处理生长记录相关的API请求
 */

const GrowthRecord = require('../models/GrowthRecord');
const { logger } = require('../middlewares/errorHandler');

class GrowthRecordController {
  /**
   * 创建生长记录
   * POST /api/v1/growth-records
   */
  static async createGrowthRecord(req, res) {
    try {
      const userId = req.user.id;
      const recordData = req.body;

      const newRecord = await GrowthRecord.create(recordData, userId);

      logger.info(`用户 ${userId} 创建了新生长记录 for plant ${newRecord.plantId}`);

      res.status(201).json({
        success: true,
        message: '生长记录创建成功',
        data: {
          growthRecord: newRecord.toJSON()
        }
      });
    } catch (error) {
      logger.error('创建生长记录失败:', error);
      
      if (error.message.includes('植物不存在')) {
        return res.status(400).json({
          error: {
            message: error.message,
            status: 400
          }
        });
      }
      
      res.status(500).json({
        error: {
          message: '创建生长记录失败，请稍后重试',
          status: 500
        }
      });
    }
  }

  /**
   * 获取生长记录列表
   * GET /api/v1/growth-records
   */
  static async getGrowthRecords(req, res) {
    try {
      const userId = req.user.id;
      const options = {
        page: parseInt(req.query.page) || 1,
        limit: parseInt(req.query.limit) || 10,
        plantId: req.query.plantId ? parseInt(req.query.plantId) : undefined,
        startDate: req.query.startDate,
        endDate: req.query.endDate
      };

      // 验证分页参数
      if (options.page < 1) options.page = 1;
      if (options.limit < 1 || options.limit > 100) options.limit = 10;

      // 验证日期格式
      if (options.startDate && isNaN(Date.parse(options.startDate))) {
        return res.status(400).json({
          error: {
            message: '开始日期格式不正确',
            status: 400
          }
        });
      }

      if (options.endDate && isNaN(Date.parse(options.endDate))) {
        return res.status(400).json({
          error: {
            message: '结束日期格式不正确',
            status: 400
          }
        });
      }

      const result = await GrowthRecord.findByUserId(userId, options);

      res.json({
        success: true,
        message: '获取生长记录列表成功',
        data: {
          growthRecords: result.growthRecords.map(record => record.toJSON()),
          pagination: {
            page: result.page,
            limit: result.limit,
            total: result.total,
            totalPages: result.totalPages
          }
        }
      });
    } catch (error) {
      logger.error('获取生长记录列表失败:', error);
      
      res.status(500).json({
        error: {
          message: '获取生长记录列表失败，请稍后重试',
          status: 500
        }
      });
    }
  }

  /**
   * 获取单个生长记录详情
   * GET /api/v1/growth-records/:id
   */
  static async getGrowthRecordById(req, res) {
    try {
      const userId = req.user.id;
      const recordId = req.params.id;

      // 验证记录ID
      if (!recordId || isNaN(recordId)) {
        return res.status(400).json({
          error: {
            message: '无效的生长记录ID',
            status: 400
          }
        });
      }

      const growthRecord = await GrowthRecord.findByIdAndUserId(recordId, userId);

      if (!growthRecord) {
        return res.status(404).json({
          error: {
            message: '生长记录不存在或不属于当前用户',
            status: 404
          }
        });
      }

      res.json({
        success: true,
        message: '获取生长记录详情成功',
        data: {
          growthRecord: growthRecord.toJSON()
        }
      });
    } catch (error) {
      logger.error('获取生长记录详情失败:', error);
      
      res.status(500).json({
        error: {
          message: '获取生长记录详情失败，请稍后重试',
          status: 500
        }
      });
    }
  }

  /**
   * 更新生长记录
   * PUT /api/v1/growth-records/:id
   */
  static async updateGrowthRecord(req, res) {
    try {
      const userId = req.user.id;
      const recordId = req.params.id;
      const updateData = req.body;

      // 验证记录ID
      if (!recordId || isNaN(recordId)) {
        return res.status(400).json({
          error: {
            message: '无效的生长记录ID',
            status: 400
          }
        });
      }

      // 检查生长记录是否存在
      const existingRecord = await GrowthRecord.findByIdAndUserId(recordId, userId);
      if (!existingRecord) {
        return res.status(404).json({
          error: {
            message: '生长记录不存在或不属于当前用户',
            status: 404
          }
        });
      }

      const updatedRecord = await GrowthRecord.updateByIdAndUserId(recordId, userId, updateData);

      if (!updatedRecord) {
        return res.status(404).json({
          error: {
            message: '更新失败，生长记录不存在',
            status: 404
          }
        });
      }

      logger.info(`用户 ${userId} 更新了生长记录 ${recordId}`);

      res.json({
        success: true,
        message: '生长记录更新成功',
        data: {
          growthRecord: updatedRecord.toJSON()
        }
      });
    } catch (error) {
      logger.error('更新生长记录失败:', error);
      
      res.status(500).json({
        error: {
          message: '更新生长记录失败，请稍后重试',
          status: 500
        }
      });
    }
  }

  /**
   * 删除生长记录
   * DELETE /api/v1/growth-records/:id
   */
  static async deleteGrowthRecord(req, res) {
    try {
      const userId = req.user.id;
      const recordId = req.params.id;

      // 验证记录ID
      if (!recordId || isNaN(recordId)) {
        return res.status(400).json({
          error: {
            message: '无效的生长记录ID',
            status: 400
          }
        });
      }

      // 检查生长记录是否存在
      const existingRecord = await GrowthRecord.findByIdAndUserId(recordId, userId);
      if (!existingRecord) {
        return res.status(404).json({
          error: {
            message: '生长记录不存在或不属于当前用户',
            status: 404
          }
        });
      }

      const deleted = await GrowthRecord.deleteByIdAndUserId(recordId, userId);

      if (!deleted) {
        return res.status(404).json({
          error: {
            message: '删除失败，生长记录不存在',
            status: 404
          }
        });
      }

      logger.info(`用户 ${userId} 删除了生长记录 ${recordId}`);

      res.json({
        success: true,
        message: '生长记录删除成功'
      });
    } catch (error) {
      logger.error('删除生长记录失败:', error);
      
      res.status(500).json({
        error: {
          message: '删除生长记录失败，请稍后重试',
          status: 500
        }
      });
    }
  }

  /**
   * 获取生长记录统计信息
   * GET /api/v1/growth-records/stats
   */
  static async getGrowthRecordStats(req, res) {
    try {
      const userId = req.user.id;
      const plantId = req.query.plantId ? parseInt(req.query.plantId) : undefined;

      // 获取生长记录
      const result = await GrowthRecord.findByUserId(userId, { 
        limit: 1000,
        plantId: plantId
      });
      const records = result.growthRecords;

      // 计算统计信息
      const stats = {
        totalRecords: records.length,
        averageHeight: 0,
        averageWidth: 0,
        averageLeafCount: 0,
        growthTrend: 'stable', // stable, growing, declining
        recentRecords: 0
      };

      if (records.length > 0) {
        // 计算平均值
        const heights = records.filter(r => r.height).map(r => r.height);
        const widths = records.filter(r => r.width).map(r => r.width);
        const leafCounts = records.filter(r => r.leafCount).map(r => r.leafCount);

        if (heights.length > 0) {
          stats.averageHeight = Math.round((heights.reduce((a, b) => a + b, 0) / heights.length) * 100) / 100;
        }

        if (widths.length > 0) {
          stats.averageWidth = Math.round((widths.reduce((a, b) => a + b, 0) / widths.length) * 100) / 100;
        }

        if (leafCounts.length > 0) {
          stats.averageLeafCount = Math.round(leafCounts.reduce((a, b) => a + b, 0) / leafCounts.length);
        }

        // 分析生长趋势（基于最近的记录）
        if (heights.length >= 2) {
          const recentHeights = heights.slice(-3); // 最近3次记录
          const trend = recentHeights[recentHeights.length - 1] - recentHeights[0];
          if (trend > 0.5) {
            stats.growthTrend = 'growing';
          } else if (trend < -0.5) {
            stats.growthTrend = 'declining';
          }
        }

        // 统计最近一周的记录
        const weekAgo = new Date();
        weekAgo.setDate(weekAgo.getDate() - 7);
        stats.recentRecords = records.filter(r => {
          const recordDate = new Date(r.recordDate);
          return recordDate > weekAgo;
        }).length;
      }

      res.json({
        success: true,
        message: '获取生长记录统计成功',
        data: {
          stats
        }
      });
    } catch (error) {
      logger.error('获取生长记录统计失败:', error);
      
      res.status(500).json({
        error: {
          message: '获取生长记录统计失败，请稍后重试',
          status: 500
        }
      });
    }
  }
}

module.exports = GrowthRecordController;
