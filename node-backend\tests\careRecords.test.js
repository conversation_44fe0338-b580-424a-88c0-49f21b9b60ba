/**
 * 护理记录CRUD API测试
 * 测试护理记录数据的增删改查功能
 */

const request = require('supertest');
const User = require('../src/models/User');
const Plant = require('../src/models/Plant');
const CareRecord = require('../src/models/CareRecord');

describe('护理记录CRUD API', () => {
  let app;
  let authToken;
  let testUserId;
  let testPlantId;

  beforeAll(async () => {
    try {
      app = require('../src/app');
      User.clearMockUsers();

      // 注册测试用户并获取认证令牌
      const userData = {
        username: 'careuser',
        email: '<EMAIL>',
        password: 'password123',
        confirmPassword: 'password123'
      };

      const registerResponse = await request(app)
        .post('/api/v1/auth/register')
        .send(userData);

      if (registerResponse.status === 201) {
        authToken = registerResponse.body.data.token;
        testUserId = registerResponse.body.data.user.id;
      }

      // 创建测试植物
      if (authToken) {
        const plantData = {
          name: '测试植物',
          species: 'Test Plant',
          description: '用于护理记录测试',
          location: '测试位置'
        };

        const plantResponse = await request(app)
          .post('/api/v1/plants')
          .set('Authorization', `Bearer ${authToken}`)
          .send(plantData);

        if (plantResponse.status === 201) {
          testPlantId = plantResponse.body.data.plant.id;
        }
      }
    } catch (error) {
      console.log('App not found - this is expected for TDD red phase');
    }
  });

  beforeEach(async () => {
    // 每个测试前清除护理记录模拟数据
    if (process.env.NODE_ENV === 'test') {
      Plant.clearMockPlants();
      CareRecord.clearMockCareRecords();

      // 重新创建测试植物
      if (authToken) {
        const plantData = {
          name: '测试植物',
          species: 'Test Plant',
          description: '用于护理记录测试',
          location: '测试位置'
        };

        const plantResponse = await request(app)
          .post('/api/v1/plants')
          .set('Authorization', `Bearer ${authToken}`)
          .send(plantData);

        if (plantResponse.status === 201) {
          testPlantId = plantResponse.body.data.plant.id;
        }
      }
    }
  });

  afterAll(async () => {
    if (app && app.close) {
      await app.close();
    }
  });

  describe('创建护理记录 POST /api/v1/care-records', () => {
    test('应该成功创建新护理记录', async () => {
      if (!app || !authToken || !testPlantId) {
        expect(true).toBe(false); // TDD红色阶段
        return;
      }

      const careData = {
        plantId: testPlantId,
        type: 'watering',
        description: '给植物浇水',
        amount: '200ml',
        notes: '土壤较干，需要充分浇水',
        careDate: '2024-01-20T10:00:00Z'
      };

      const response = await request(app)
        .post('/api/v1/care-records')
        .set('Authorization', `Bearer ${authToken}`)
        .send(careData)
        .expect(201);

      expect(response.body).toHaveProperty('success', true);
      expect(response.body).toHaveProperty('message');
      expect(response.body).toHaveProperty('data');
      expect(response.body.data).toHaveProperty('careRecord');
      expect(response.body.data.careRecord).toHaveProperty('id');
      expect(response.body.data.careRecord).toHaveProperty('type', 'watering');
      expect(response.body.data.careRecord).toHaveProperty('plantId', testPlantId);
      expect(response.body.data.careRecord).toHaveProperty('userId', testUserId);
    });

    test('应该验证护理类型', async () => {
      if (!app || !authToken || !testPlantId) {
        expect(true).toBe(false);
        return;
      }

      const careData = {
        plantId: testPlantId,
        type: 'invalid_type',
        description: '无效的护理类型'
      };

      const response = await request(app)
        .post('/api/v1/care-records')
        .set('Authorization', `Bearer ${authToken}`)
        .send(careData)
        .expect(400);

      expect(response.body).toHaveProperty('error');
      expect(response.body.error).toHaveProperty('message');
    });

    test('应该验证植物存在性', async () => {
      if (!app || !authToken) {
        expect(true).toBe(false);
        return;
      }

      const careData = {
        plantId: 999,
        type: 'watering',
        description: '给不存在的植物浇水'
      };

      const response = await request(app)
        .post('/api/v1/care-records')
        .set('Authorization', `Bearer ${authToken}`)
        .send(careData)
        .expect(400);

      expect(response.body).toHaveProperty('error');
      expect(response.body.error.message).toContain('植物不存在');
    });
  });

  describe('获取护理记录列表 GET /api/v1/care-records', () => {
    test('应该返回用户的护理记录列表', async () => {
      if (!app || !authToken || !testPlantId) {
        expect(true).toBe(false);
        return;
      }

      // 先创建一个护理记录
      const careData = {
        plantId: testPlantId,
        type: 'fertilizing',
        description: '施肥',
        amount: '10ml',
        notes: '使用液体肥料'
      };

      await request(app)
        .post('/api/v1/care-records')
        .set('Authorization', `Bearer ${authToken}`)
        .send(careData)
        .expect(201);

      // 获取护理记录列表
      const response = await request(app)
        .get('/api/v1/care-records')
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200);

      expect(response.body).toHaveProperty('success', true);
      expect(response.body).toHaveProperty('data');
      expect(response.body.data).toHaveProperty('careRecords');
      expect(Array.isArray(response.body.data.careRecords)).toBe(true);
      expect(response.body.data.careRecords.length).toBeGreaterThan(0);
    });

    test('应该支持按植物ID筛选', async () => {
      if (!app || !authToken || !testPlantId) {
        expect(true).toBe(false);
        return;
      }

      const response = await request(app)
        .get(`/api/v1/care-records?plantId=${testPlantId}`)
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200);

      expect(response.body).toHaveProperty('success', true);
      expect(response.body.data).toHaveProperty('careRecords');
    });

    test('应该支持按护理类型筛选', async () => {
      if (!app || !authToken) {
        expect(true).toBe(false);
        return;
      }

      const response = await request(app)
        .get('/api/v1/care-records?type=watering')
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200);

      expect(response.body).toHaveProperty('success', true);
      expect(response.body.data).toHaveProperty('careRecords');
    });

    test('应该支持按日期范围筛选', async () => {
      if (!app || !authToken) {
        expect(true).toBe(false);
        return;
      }

      const startDate = '2024-01-01';
      const endDate = '2024-01-31';

      const response = await request(app)
        .get(`/api/v1/care-records?startDate=${startDate}&endDate=${endDate}`)
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200);

      expect(response.body).toHaveProperty('success', true);
      expect(response.body.data).toHaveProperty('careRecords');
    });
  });

  describe('获取单个护理记录 GET /api/v1/care-records/:id', () => {
    test('应该返回指定护理记录的详细信息', async () => {
      if (!app || !authToken || !testPlantId) {
        expect(true).toBe(false);
        return;
      }

      // 先创建一个护理记录
      const careData = {
        plantId: testPlantId,
        type: 'pruning',
        description: '修剪枯叶',
        notes: '移除黄叶和枯枝'
      };

      const createResponse = await request(app)
        .post('/api/v1/care-records')
        .set('Authorization', `Bearer ${authToken}`)
        .send(careData)
        .expect(201);

      const careRecordId = createResponse.body.data.careRecord.id;

      // 获取护理记录详情
      const response = await request(app)
        .get(`/api/v1/care-records/${careRecordId}`)
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200);

      expect(response.body).toHaveProperty('success', true);
      expect(response.body.data).toHaveProperty('careRecord');
      expect(response.body.data.careRecord).toHaveProperty('id', careRecordId);
      expect(response.body.data.careRecord).toHaveProperty('type', 'pruning');
    });
  });

  describe('更新护理记录 PUT /api/v1/care-records/:id', () => {
    test('应该成功更新护理记录', async () => {
      if (!app || !authToken || !testPlantId) {
        expect(true).toBe(false);
        return;
      }

      // 先创建一个护理记录
      const careData = {
        plantId: testPlantId,
        type: 'watering',
        description: '浇水',
        amount: '100ml'
      };

      const createResponse = await request(app)
        .post('/api/v1/care-records')
        .set('Authorization', `Bearer ${authToken}`)
        .send(careData)
        .expect(201);

      const careRecordId = createResponse.body.data.careRecord.id;

      // 更新护理记录
      const updateData = {
        description: '深度浇水',
        amount: '300ml',
        notes: '土壤很干，增加浇水量'
      };

      const response = await request(app)
        .put(`/api/v1/care-records/${careRecordId}`)
        .set('Authorization', `Bearer ${authToken}`)
        .send(updateData)
        .expect(200);

      expect(response.body).toHaveProperty('success', true);
      expect(response.body.data.careRecord).toHaveProperty('description', '深度浇水');
      expect(response.body.data.careRecord).toHaveProperty('amount', '300ml');
    });
  });

  describe('删除护理记录 DELETE /api/v1/care-records/:id', () => {
    test('应该成功删除护理记录', async () => {
      if (!app || !authToken || !testPlantId) {
        expect(true).toBe(false);
        return;
      }

      // 先创建一个护理记录
      const careData = {
        plantId: testPlantId,
        type: 'watering',
        description: '待删除的护理记录'
      };

      const createResponse = await request(app)
        .post('/api/v1/care-records')
        .set('Authorization', `Bearer ${authToken}`)
        .send(careData)
        .expect(201);

      const careRecordId = createResponse.body.data.careRecord.id;

      // 删除护理记录
      const response = await request(app)
        .delete(`/api/v1/care-records/${careRecordId}`)
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200);

      expect(response.body).toHaveProperty('success', true);
      expect(response.body).toHaveProperty('message');

      // 验证护理记录已被删除
      await request(app)
        .get(`/api/v1/care-records/${careRecordId}`)
        .set('Authorization', `Bearer ${authToken}`)
        .expect(404);
    });
  });
});
