# Multi-stage Docker build for PlantApp Backend with PostgreSQL and Redis
FROM ubuntu:22.04

# Avoid prompts from apt
ENV DEBIAN_FRONTEND=noninteractive

# Set working directory
WORKDIR /app

# Install system dependencies
RUN apt-get update && apt-get install -y \
    curl \
    wget \
    gnupg \
    lsb-release \
    ca-certificates \
    supervisor \
    && rm -rf /var/lib/apt/lists/*

# Install Node.js 18.x
RUN curl -fsSL https://deb.nodesource.com/setup_18.x | bash - \
    && apt-get install -y nodejs

# Install PostgreSQL
RUN apt-get update && apt-get install -y \
    postgresql \
    postgresql-contrib \
    postgresql-client \
    && rm -rf /var/lib/apt/lists/*

# Install Redis
RUN apt-get update && apt-get install -y \
    redis-server \
    && rm -rf /var/lib/apt/lists/*

# Create app directory and copy package files
COPY package*.json ./

# Install Node.js dependencies
RUN npm ci --only=production

# Copy application code
COPY . .

# Create necessary directories and set environment variables
RUN mkdir -p /var/log/supervisor \
    && mkdir -p /app/logs \
    && mkdir -p /var/lib/postgresql/data \
    && mkdir -p /var/lib/redis

# Set PostgreSQL environment variables
ENV PGDATA=/var/lib/postgresql/data
ENV POSTGRES_USER=postgres
ENV POSTGRES_DB=postgres

# Configure PostgreSQL
USER postgres
RUN /etc/init.d/postgresql start && \
    psql --command "CREATE USER plantapp_user WITH PASSWORD 'secure_password';" && \
    psql --command "CREATE DATABASE plantapp_dev OWNER plantapp_user;" && \
    psql --command "GRANT ALL PRIVILEGES ON DATABASE plantapp_dev TO plantapp_user;" && \
    /etc/init.d/postgresql stop

# Switch back to root user
USER root

# Configure Redis
RUN echo "bind 127.0.0.1" >> /etc/redis/redis.conf \
    && echo "port 6379" >> /etc/redis/redis.conf \
    && echo "daemonize no" >> /etc/redis/redis.conf

# Create supervisor configuration
COPY docker/supervisord.conf /etc/supervisor/conf.d/supervisord.conf

# Copy environment configuration for Docker
COPY docker/.env.docker .env

# Copy startup script and make it executable
COPY docker/startup.sh /app/startup.sh
RUN chmod +x /app/startup.sh

# Set proper permissions
RUN chown -R postgres:postgres /var/lib/postgresql \
    && chmod 700 /var/lib/postgresql/data \
    && chown -R redis:redis /var/lib/redis

# Expose ports
EXPOSE 3000 5432 6379

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=60s --retries=3 \
    CMD curl -f http://localhost:3000/health || exit 1

# Start services using startup script
CMD ["/app/startup.sh"]
