# OpenRouter API 集成文档

## 概述

本文档描述了PlantApp后端与OpenRouter API的集成，实现多模态植物识别功能。

## 功能特性

- **多模态输入支持**: 图像、文本描述、分子数据
- **智能植物识别**: 基于AI的植物种类识别
- **护理建议生成**: 个性化的植物护理建议
- **批量处理**: 支持批量植物识别
- **错误处理**: 完善的错误处理和重试机制

## API 端点

### 1. 植物识别

**POST** `/api/v1/identification/identify`

识别植物种类，支持多种输入方式。

#### 请求参数

| 参数 | 类型 | 必需 | 描述 |
|------|------|------|------|
| `textDescription` | string | 否 | 植物的文字描述 (1-2000字符) |
| `molecularData` | string | 否 | 分子数据 (DNA序列或FASTA格式) |
| `imageBase64` | string | 否 | base64编码的图像数据 |
| `image` | file | 否 | 图像文件 (multipart/form-data) |
| `options.model` | string | 否 | 使用的AI模型 |
| `options.temperature` | number | 否 | 生成温度 (0-2) |

**注意**: 至少需要提供一种输入类型。

#### 请求示例

```json
{
  "textDescription": "一种有着大型心形叶子的室内植物，叶子上有天然的孔洞",
  "options": {
    "model": "anthropic/claude-3-sonnet",
    "temperature": 0.7
  }
}
```

#### 响应示例

```json
{
  "success": true,
  "data": {
    "scientificName": "Monstera deliciosa",
    "commonName": "龟背竹",
    "confidence": 95,
    "features": [
      "大型心形叶子",
      "叶子上有天然孔洞",
      "攀援性植物"
    ],
    "family": "天南星科",
    "genus": "龟背竹属",
    "careRequirements": {
      "light": "明亮的间接光",
      "water": "土壤表面干燥时浇水",
      "humidity": "中等到高湿度"
    },
    "habitat": "中美洲热带雨林",
    "timestamp": "2024-01-01T12:00:00.000Z"
  },
  "message": "Plant identification completed successfully"
}
```

### 2. 护理建议

**POST** `/api/v1/identification/care-advice`

获取特定植物的护理建议。

#### 请求参数

| 参数 | 类型 | 必需 | 描述 |
|------|------|------|------|
| `plantName` | string | 是 | 植物名称 (1-200字符) |
| `conditions.location` | string | 否 | 位置 (室内/室外) |
| `conditions.climate` | string | 否 | 气候类型 |
| `conditions.season` | string | 否 | 季节 (spring/summer/autumn/winter) |

#### 请求示例

```json
{
  "plantName": "Monstera deliciosa",
  "conditions": {
    "location": "室内",
    "climate": "温带",
    "season": "summer"
  }
}
```

#### 响应示例

```json
{
  "success": true,
  "data": {
    "watering": "夏季每周浇水1-2次，保持土壤微湿但不积水",
    "light": "明亮的间接光，避免直射阳光",
    "soil": "排水良好的盆栽土，可添加珍珠岩增加透气性",
    "temperature": "18-27°C，避免低于15°C",
    "humidity": "50-60%，可通过喷雾或加湿器增加湿度",
    "fertilization": "生长季节每月施用稀释的液体肥料",
    "problems": [
      "叶子发黄：可能是浇水过多",
      "叶子边缘褐色：湿度不足",
      "生长缓慢：光照不足"
    ],
    "seasonal": {
      "summer": "增加浇水频率，保持高湿度",
      "winter": "减少浇水，停止施肥"
    },
    "timestamp": "2024-01-01T12:00:00.000Z"
  },
  "message": "Care advice generated successfully"
}
```

### 3. 批量识别

**POST** `/api/v1/identification/batch-identify`

批量识别多个植物（需要认证）。

#### 请求参数

| 参数 | 类型 | 必需 | 描述 |
|------|------|------|------|
| `items` | array | 是 | 识别项目数组 (最多10个) |
| `items[].textDescription` | string | 否 | 植物描述 |
| `items[].molecularData` | string | 否 | 分子数据 |
| `items[].imageBase64` | string | 否 | 图像数据 |
| `options` | object | 否 | 识别选项 |

#### 响应示例

```json
{
  "success": true,
  "data": {
    "results": [
      {
        "index": 0,
        "success": true,
        "data": { /* 识别结果 */ }
      }
    ],
    "errors": [
      {
        "index": 1,
        "success": false,
        "error": "识别失败原因"
      }
    ],
    "summary": {
      "total": 2,
      "successful": 1,
      "failed": 1
    }
  },
  "message": "Batch identification completed"
}
```

### 4. 识别历史

**GET** `/api/v1/identification/history`

获取用户的识别历史（需要认证）。

#### 查询参数

| 参数 | 类型 | 默认值 | 描述 |
|------|------|--------|------|
| `page` | number | 1 | 页码 |
| `limit` | number | 20 | 每页数量 (1-100) |
| `startDate` | string | - | 开始日期 (ISO 8601) |
| `endDate` | string | - | 结束日期 (ISO 8601) |

### 5. 服务状态

**GET** `/api/v1/identification/service-status`

检查OpenRouter服务状态。

#### 响应示例

```json
{
  "success": true,
  "data": {
    "status": "healthy",
    "timestamp": "2024-01-01T12:00:00.000Z",
    "apiKey": true,
    "baseURL": "https://openrouter.ai/api/v1"
  },
  "message": "OpenRouter service is healthy"
}
```

## 错误处理

### 错误响应格式

```json
{
  "success": false,
  "message": "错误描述",
  "error": "详细错误信息"
}
```

### 常见错误码

| 状态码 | 描述 |
|--------|------|
| 400 | 请求参数错误 |
| 401 | 认证失败 |
| 429 | 请求频率限制 |
| 500 | 服务器内部错误 |
| 503 | OpenRouter服务不可用 |

## 配置

### 环境变量

```bash
# OpenRouter API配置
OPENROUTER_API_KEY=your_api_key_here
OPENROUTER_BASE_URL=https://openrouter.ai/api/v1
OPENROUTER_DEFAULT_MODEL=anthropic/claude-3-sonnet
OPENROUTER_MAX_RETRIES=3
OPENROUTER_TIMEOUT=30000

# 应用配置
APP_URL=http://localhost:3000
```

### 支持的模型

- `anthropic/claude-3-sonnet`
- `anthropic/claude-3-haiku`
- `openai/gpt-4-vision-preview`
- `google/gemini-pro-vision`

## 使用限制

- 图像文件大小: 最大10MB
- 文本描述: 最大2000字符
- 分子数据: 最大50000字符
- 批量识别: 最多10个项目
- 请求频率: 根据配置的速率限制

## 最佳实践

1. **输入质量**: 提供清晰的图像和详细的描述以获得更好的识别结果
2. **错误处理**: 实现适当的错误处理和重试逻辑
3. **缓存**: 对相同输入的结果进行缓存以减少API调用
4. **监控**: 监控API使用情况和错误率
5. **安全**: 保护API密钥，不要在客户端暴露
