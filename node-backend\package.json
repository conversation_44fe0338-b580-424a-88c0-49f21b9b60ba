{"name": "plantapp-backend", "version": "1.0.0", "description": "PlantApp Node.js Backend API Service", "main": "src/app.js", "scripts": {"start": "node src/app.js", "dev": "nodemon src/app.js", "test": "cross-env NODE_ENV=test jest", "test:watch": "cross-env NODE_ENV=test jest --watch", "test:coverage": "jest --coverage", "lint": "eslint src/ --ext .js", "lint:fix": "eslint src/ --ext .js --fix", "build": "echo 'Build completed'", "migrate": "node scripts/migrate.js", "seed": "node scripts/seed.js"}, "keywords": ["plantapp", "nodejs", "express", "api", "postgresql", "redis"], "author": "PlantApp Team", "license": "MIT", "dependencies": {"express": "^4.18.2", "cors": "^2.8.5", "helmet": "^7.1.0", "morgan": "^1.10.0", "compression": "^1.7.4", "dotenv": "^16.3.1", "pg": "^8.11.3", "redis": "^4.6.10", "bcryptjs": "^2.4.3", "jsonwebtoken": "^9.0.2", "joi": "^17.11.0", "express-rate-limit": "^7.1.5", "express-validator": "^7.0.1", "multer": "^1.4.5-lts.1", "axios": "^1.6.2", "winston": "^3.11.0", "uuid": "^9.0.1"}, "devDependencies": {"jest": "^29.7.0", "supertest": "^6.3.3", "nodemon": "^3.0.2", "eslint": "^8.55.0", "eslint-config-standard": "^17.1.0", "eslint-plugin-import": "^2.29.0", "eslint-plugin-node": "^11.1.0", "eslint-plugin-promise": "^6.1.1", "cross-env": "^7.0.3"}, "engines": {"node": ">=16.0.0", "npm": ">=8.0.0"}, "jest": {"testEnvironment": "node", "collectCoverageFrom": ["src/**/*.js", "!src/app.js", "!src/config/*.js"], "coverageDirectory": "coverage", "coverageReporters": ["text", "lcov", "html"]}}