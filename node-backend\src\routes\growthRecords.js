/**
 * 生长记录路由
 * 处理生长记录相关的API端点
 */

const express = require('express');
const router = express.Router();

const GrowthRecordController = require('../controllers/growthRecordController');
const { authenticateToken } = require('../middlewares/auth');
const { 
  validate, 
  growthRecordCreateValidation, 
  growthRecordUpdateValidation 
} = require('../middlewares/validation');

/**
 * 获取生长记录统计信息
 * GET /api/v1/growth-records/stats
 */
router.get('/stats', 
  authenticateToken,
  GrowthRecordController.getGrowthRecordStats
);

/**
 * 创建生长记录
 * POST /api/v1/growth-records
 */
router.post('/', 
  authenticateToken,
  validate(growthRecordCreateValidation),
  GrowthRecordController.createGrowthRecord
);

/**
 * 获取生长记录列表
 * GET /api/v1/growth-records
 */
router.get('/', 
  authenticateToken,
  GrowthRecordController.getGrowthRecords
);

/**
 * 获取单个生长记录详情
 * GET /api/v1/growth-records/:id
 */
router.get('/:id', 
  authenticateToken,
  GrowthRecordController.getGrowthRecordById
);

/**
 * 更新生长记录
 * PUT /api/v1/growth-records/:id
 */
router.put('/:id', 
  authenticateToken,
  validate(growthRecordUpdateValidation),
  GrowthRecordController.updateGrowthRecord
);

/**
 * 删除生长记录
 * DELETE /api/v1/growth-records/:id
 */
router.delete('/:id', 
  authenticateToken,
  GrowthRecordController.deleteGrowthRecord
);

module.exports = router;
