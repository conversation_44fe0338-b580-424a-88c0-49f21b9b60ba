/**
 * 生长记录CRUD API测试
 * 测试生长记录数据的增删改查功能
 */

const request = require('supertest');
const User = require('../src/models/User');
const Plant = require('../src/models/Plant');
const GrowthRecord = require('../src/models/GrowthRecord');

describe('生长记录CRUD API', () => {
  let app;
  let authToken;
  let testUserId;
  let testPlantId;

  beforeAll(async () => {
    try {
      app = require('../src/app');
      User.clearMockUsers();
      Plant.clearMockPlants();

      // 注册测试用户并获取认证令牌
      const userData = {
        username: 'growthuser',
        email: '<EMAIL>',
        password: 'password123',
        confirmPassword: 'password123'
      };

      const registerResponse = await request(app)
        .post('/api/v1/auth/register')
        .send(userData);

      if (registerResponse.status === 201) {
        authToken = registerResponse.body.data.token;
        testUserId = registerResponse.body.data.user.id;
      }

      // 创建测试植物
      if (authToken) {
        const plantData = {
          name: '测试植物',
          species: 'Test Plant',
          description: '用于生长记录测试',
          location: '测试位置'
        };

        const plantResponse = await request(app)
          .post('/api/v1/plants')
          .set('Authorization', `Bearer ${authToken}`)
          .send(plantData);

        if (plantResponse.status === 201) {
          testPlantId = plantResponse.body.data.plant.id;
        }
      }
    } catch (error) {
      console.log('App not found - this is expected for TDD red phase');
    }
  });

  beforeEach(async () => {
    // 每个测试前清除生长记录模拟数据
    if (process.env.NODE_ENV === 'test') {
      Plant.clearMockPlants();
      GrowthRecord.clearMockRecords();

      // 重新创建测试植物
      if (authToken) {
        const plantData = {
          name: '测试植物',
          species: 'Test Plant',
          description: '用于生长记录测试',
          location: '测试位置'
        };

        const plantResponse = await request(app)
          .post('/api/v1/plants')
          .set('Authorization', `Bearer ${authToken}`)
          .send(plantData);

        if (plantResponse.status === 201) {
          testPlantId = plantResponse.body.data.plant.id;
        }
      }
    }
  });

  afterAll(async () => {
    if (app && app.close) {
      await app.close();
    }
  });

  describe('创建生长记录 POST /api/v1/growth-records', () => {
    test('应该成功创建新生长记录', async () => {
      if (!app || !authToken || !testPlantId) {
        expect(true).toBe(false); // TDD红色阶段
        return;
      }

      const growthData = {
        plantId: testPlantId,
        height: 15.5,
        width: 8.2,
        leafCount: 12,
        notes: '植物生长良好，新叶片开始展开',
        images: ['data:image/jpeg;base64,/9j/4AAQSkZJRgABAQAAAQABAAD/2wBDAAYEBQYFBAYGBQYHBwYIChAKCgkJChQODwwQFxQYGBcUFhYaHSUfGhsjHBYWICwgIyYnKSopGR8tMC0oMCUoKSj/2wBDAQcHBwoIChMKChMoGhYaKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCj/wAARCAABAAEDASIAAhEBAxEB/8QAFQABAQAAAAAAAAAAAAAAAAAAAAv/xAAUEAEAAAAAAAAAAAAAAAAAAAAA/8QAFQEBAQAAAAAAAAAAAAAAAAAAAAX/xAAUEQEAAAAAAAAAAAAAAAAAAAAA/9oADAMBAAIRAxEAPwCdABmX/9k='],
        recordDate: '2024-01-20T10:00:00Z'
      };

      const response = await request(app)
        .post('/api/v1/growth-records')
        .set('Authorization', `Bearer ${authToken}`)
        .send(growthData)
        .expect(201);

      expect(response.body).toHaveProperty('success', true);
      expect(response.body).toHaveProperty('message');
      expect(response.body).toHaveProperty('data');
      expect(response.body.data).toHaveProperty('growthRecord');
      expect(response.body.data.growthRecord).toHaveProperty('id');
      expect(response.body.data.growthRecord).toHaveProperty('height', 15.5);
      expect(response.body.data.growthRecord).toHaveProperty('plantId', testPlantId);
      expect(response.body.data.growthRecord).toHaveProperty('userId', testUserId);
    });

    test('应该验证植物存在性', async () => {
      if (!app || !authToken) {
        expect(true).toBe(false);
        return;
      }

      const growthData = {
        plantId: 999,
        height: 15.5,
        notes: '给不存在的植物记录生长'
      };

      const response = await request(app)
        .post('/api/v1/growth-records')
        .set('Authorization', `Bearer ${authToken}`)
        .send(growthData)
        .expect(400);

      expect(response.body).toHaveProperty('error');
      expect(response.body.error.message).toContain('植物不存在');
    });

    test('应该验证必填字段', async () => {
      if (!app || !authToken) {
        expect(true).toBe(false);
        return;
      }

      const response = await request(app)
        .post('/api/v1/growth-records')
        .set('Authorization', `Bearer ${authToken}`)
        .send({})
        .expect(400);

      expect(response.body).toHaveProperty('error');
      expect(response.body.error).toHaveProperty('message');
    });
  });

  describe('获取生长记录列表 GET /api/v1/growth-records', () => {
    test('应该返回用户的生长记录列表', async () => {
      if (!app || !authToken || !testPlantId) {
        expect(true).toBe(false);
        return;
      }

      // 先创建一个生长记录
      const growthData = {
        plantId: testPlantId,
        height: 20.0,
        width: 10.5,
        notes: '测试生长记录'
      };

      await request(app)
        .post('/api/v1/growth-records')
        .set('Authorization', `Bearer ${authToken}`)
        .send(growthData)
        .expect(201);

      // 获取生长记录列表
      const response = await request(app)
        .get('/api/v1/growth-records')
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200);

      expect(response.body).toHaveProperty('success', true);
      expect(response.body).toHaveProperty('data');
      expect(response.body.data).toHaveProperty('growthRecords');
      expect(Array.isArray(response.body.data.growthRecords)).toBe(true);
      expect(response.body.data.growthRecords.length).toBeGreaterThan(0);
    });

    test('应该支持按植物ID筛选', async () => {
      if (!app || !authToken || !testPlantId) {
        expect(true).toBe(false);
        return;
      }

      const response = await request(app)
        .get(`/api/v1/growth-records?plantId=${testPlantId}`)
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200);

      expect(response.body).toHaveProperty('success', true);
      expect(response.body.data).toHaveProperty('growthRecords');
    });

    test('应该支持按日期范围筛选', async () => {
      if (!app || !authToken) {
        expect(true).toBe(false);
        return;
      }

      const startDate = '2024-01-01';
      const endDate = '2024-01-31';

      const response = await request(app)
        .get(`/api/v1/growth-records?startDate=${startDate}&endDate=${endDate}`)
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200);

      expect(response.body).toHaveProperty('success', true);
      expect(response.body.data).toHaveProperty('growthRecords');
    });
  });

  describe('获取单个生长记录 GET /api/v1/growth-records/:id', () => {
    test('应该返回指定生长记录的详细信息', async () => {
      if (!app || !authToken || !testPlantId) {
        expect(true).toBe(false);
        return;
      }

      // 先创建一个生长记录
      const growthData = {
        plantId: testPlantId,
        height: 25.0,
        notes: '详细生长记录'
      };

      const createResponse = await request(app)
        .post('/api/v1/growth-records')
        .set('Authorization', `Bearer ${authToken}`)
        .send(growthData)
        .expect(201);

      const recordId = createResponse.body.data.growthRecord.id;

      // 获取生长记录详情
      const response = await request(app)
        .get(`/api/v1/growth-records/${recordId}`)
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200);

      expect(response.body).toHaveProperty('success', true);
      expect(response.body.data).toHaveProperty('growthRecord');
      expect(response.body.data.growthRecord).toHaveProperty('id', recordId);
      expect(response.body.data.growthRecord).toHaveProperty('height', 25.0);
    });
  });

  describe('更新生长记录 PUT /api/v1/growth-records/:id', () => {
    test('应该成功更新生长记录', async () => {
      if (!app || !authToken || !testPlantId) {
        expect(true).toBe(false);
        return;
      }

      // 先创建一个生长记录
      const growthData = {
        plantId: testPlantId,
        height: 30.0,
        notes: '待更新的记录'
      };

      const createResponse = await request(app)
        .post('/api/v1/growth-records')
        .set('Authorization', `Bearer ${authToken}`)
        .send(growthData)
        .expect(201);

      const recordId = createResponse.body.data.growthRecord.id;

      // 更新生长记录
      const updateData = {
        height: 32.5,
        width: 12.0,
        notes: '更新后的生长记录'
      };

      const response = await request(app)
        .put(`/api/v1/growth-records/${recordId}`)
        .set('Authorization', `Bearer ${authToken}`)
        .send(updateData)
        .expect(200);

      expect(response.body).toHaveProperty('success', true);
      expect(response.body.data.growthRecord).toHaveProperty('height', 32.5);
      expect(response.body.data.growthRecord).toHaveProperty('width', 12.0);
    });
  });

  describe('删除生长记录 DELETE /api/v1/growth-records/:id', () => {
    test('应该成功删除生长记录', async () => {
      if (!app || !authToken || !testPlantId) {
        expect(true).toBe(false);
        return;
      }

      // 先创建一个生长记录
      const growthData = {
        plantId: testPlantId,
        height: 35.0,
        notes: '待删除的生长记录'
      };

      const createResponse = await request(app)
        .post('/api/v1/growth-records')
        .set('Authorization', `Bearer ${authToken}`)
        .send(growthData)
        .expect(201);

      const recordId = createResponse.body.data.growthRecord.id;

      // 删除生长记录
      const response = await request(app)
        .delete(`/api/v1/growth-records/${recordId}`)
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200);

      expect(response.body).toHaveProperty('success', true);
      expect(response.body).toHaveProperty('message');

      // 验证生长记录已被删除
      await request(app)
        .get(`/api/v1/growth-records/${recordId}`)
        .set('Authorization', `Bearer ${authToken}`)
        .expect(404);
    });
  });
});
