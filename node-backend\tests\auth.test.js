/**
 * 用户认证与授权系统测试
 * 测试用户注册、登录、JWT验证等功能
 */

const request = require('supertest');
const bcrypt = require('bcryptjs');
const User = require('../src/models/User');

describe('用户认证与授权系统', () => {
  let app;

  beforeAll(async () => {
    try {
      app = require('../src/app');
      // 清除测试数据
      User.clearMockUsers();
    } catch (error) {
      console.log('App not found - this is expected for TDD red phase');
    }
  });

  beforeEach(() => {
    // 每个测试前清除模拟用户数据（除了某些需要保持数据的测试）
    if (process.env.NODE_ENV === 'test') {
      User.clearMockUsers();
    }
  });

  afterAll(async () => {
    if (app && app.close) {
      await app.close();
    }
  });

  describe('用户注册 POST /api/v1/auth/register', () => {
    test('应该成功注册新用户', async () => {
      if (!app) {
        expect(true).toBe(false); // TDD红色阶段
        return;
      }

      const userData = {
        username: 'testuser',
        email: '<EMAIL>',
        password: 'password123',
        confirmPassword: 'password123'
      };

      const response = await request(app)
        .post('/api/v1/auth/register')
        .send(userData)
        .expect(201);

      expect(response.body).toHaveProperty('success', true);
      expect(response.body).toHaveProperty('message');
      expect(response.body).toHaveProperty('data');
      expect(response.body.data).toHaveProperty('user');
      expect(response.body.data).toHaveProperty('token');
      expect(response.body.data.user).toHaveProperty('id');
      expect(response.body.data.user).toHaveProperty('username', 'testuser');
      expect(response.body.data.user).toHaveProperty('email', '<EMAIL>');
      expect(response.body.data.user).not.toHaveProperty('password');
    });

    test('应该拒绝重复的邮箱注册', async () => {
      if (!app) {
        expect(true).toBe(false);
        return;
      }

      const userData = {
        username: 'testuser',
        email: '<EMAIL>',
        password: 'password123',
        confirmPassword: 'password123'
      };

      // 先注册一个用户
      await request(app)
        .post('/api/v1/auth/register')
        .send(userData)
        .expect(201);

      // 尝试用相同邮箱再次注册
      const duplicateUserData = {
        username: 'testuser2',
        email: '<EMAIL>', // 重复邮箱
        password: 'password123',
        confirmPassword: 'password123'
      };

      const response = await request(app)
        .post('/api/v1/auth/register')
        .send(duplicateUserData)
        .expect(400);

      expect(response.body).toHaveProperty('error');
      expect(response.body.error).toHaveProperty('message');
      expect(response.body.error.message).toContain('邮箱已存在');
    });

    test('应该验证密码确认匹配', async () => {
      if (!app) {
        expect(true).toBe(false);
        return;
      }

      const userData = {
        username: 'testuser3',
        email: '<EMAIL>',
        password: 'password123',
        confirmPassword: 'password456' // 不匹配
      };

      const response = await request(app)
        .post('/api/v1/auth/register')
        .send(userData)
        .expect(400);

      expect(response.body).toHaveProperty('error');
      expect(response.body.error.message).toContain('密码确认不匹配');
    });

    test('应该验证必填字段', async () => {
      if (!app) {
        expect(true).toBe(false);
        return;
      }

      const response = await request(app)
        .post('/api/v1/auth/register')
        .send({})
        .expect(400);

      expect(response.body).toHaveProperty('error');
      expect(response.body.error).toHaveProperty('message');
    });
  });

  describe('用户登录 POST /api/v1/auth/login', () => {
    test('应该成功登录已注册用户', async () => {
      if (!app) {
        expect(true).toBe(false);
        return;
      }

      // 先注册用户
      const userData = {
        username: 'loginuser',
        email: '<EMAIL>',
        password: 'password123',
        confirmPassword: 'password123'
      };

      await request(app)
        .post('/api/v1/auth/register')
        .send(userData)
        .expect(201);

      // 然后登录
      const loginData = {
        email: '<EMAIL>',
        password: 'password123'
      };

      const response = await request(app)
        .post('/api/v1/auth/login')
        .send(loginData)
        .expect(200);

      expect(response.body).toHaveProperty('success', true);
      expect(response.body).toHaveProperty('data');
      expect(response.body.data).toHaveProperty('user');
      expect(response.body.data).toHaveProperty('token');
      expect(response.body.data).toHaveProperty('refreshToken');
      expect(response.body.data.user).toHaveProperty('email', '<EMAIL>');
      expect(response.body.data.user).not.toHaveProperty('password');
    });

    test('应该拒绝错误的密码', async () => {
      if (!app) {
        expect(true).toBe(false);
        return;
      }

      // 先注册用户
      const userData = {
        username: 'wrongpassuser',
        email: '<EMAIL>',
        password: 'password123',
        confirmPassword: 'password123'
      };

      await request(app)
        .post('/api/v1/auth/register')
        .send(userData)
        .expect(201);

      // 尝试用错误密码登录
      const loginData = {
        email: '<EMAIL>',
        password: 'wrongpassword'
      };

      const response = await request(app)
        .post('/api/v1/auth/login')
        .send(loginData)
        .expect(401);

      expect(response.body).toHaveProperty('error');
      expect(response.body.error.message).toContain('邮箱或密码错误');
    });

    test('应该拒绝不存在的邮箱', async () => {
      if (!app) {
        expect(true).toBe(false);
        return;
      }

      const loginData = {
        email: '<EMAIL>',
        password: 'password123'
      };

      const response = await request(app)
        .post('/api/v1/auth/login')
        .send(loginData)
        .expect(401);

      expect(response.body).toHaveProperty('error');
      expect(response.body.error.message).toContain('邮箱或密码错误');
    });
  });

  describe('JWT验证中间件', () => {
    test('应该允许带有效token的请求访问受保护路由', async () => {
      if (!app) {
        expect(true).toBe(false);
        return;
      }

      // 先注册用户获取token
      const userData = {
        username: 'tokenuser',
        email: '<EMAIL>',
        password: 'password123',
        confirmPassword: 'password123'
      };

      const registerResponse = await request(app)
        .post('/api/v1/auth/register')
        .send(userData)
        .expect(201);

      const validToken = registerResponse.body.data.token;

      const response = await request(app)
        .get('/api/v1/auth/profile')
        .set('Authorization', `Bearer ${validToken}`)
        .expect(200);

      expect(response.body).toHaveProperty('success', true);
      expect(response.body).toHaveProperty('data');
      expect(response.body.data).toHaveProperty('user');
    });

    test('应该拒绝没有token的请求', async () => {
      if (!app) {
        expect(true).toBe(false);
        return;
      }

      const response = await request(app)
        .get('/api/v1/auth/profile')
        .expect(401);

      expect(response.body).toHaveProperty('error');
      expect(response.body.error.message).toContain('未提供认证令牌');
    });

    test('应该拒绝无效token的请求', async () => {
      if (!app) {
        expect(true).toBe(false);
        return;
      }

      const response = await request(app)
        .get('/api/v1/auth/profile')
        .set('Authorization', 'Bearer invalid_token')
        .expect(401);

      expect(response.body).toHaveProperty('error');
      expect(response.body.error.message).toContain('无效的认证令牌');
    });
  });

  describe('刷新令牌 POST /api/v1/auth/refresh', () => {
    test('应该使用有效的刷新令牌获取新的访问令牌', async () => {
      if (!app) {
        expect(true).toBe(false);
        return;
      }

      // 先注册用户
      const userData = {
        username: 'refreshuser',
        email: '<EMAIL>',
        password: 'password123',
        confirmPassword: 'password123'
      };

      await request(app)
        .post('/api/v1/auth/register')
        .send(userData)
        .expect(201);

      // 然后登录获取刷新令牌
      const loginData = {
        email: '<EMAIL>',
        password: 'password123'
      };

      const loginResponse = await request(app)
        .post('/api/v1/auth/login')
        .send(loginData)
        .expect(200);

      const refreshToken = loginResponse.body.data.refreshToken;

      const response = await request(app)
        .post('/api/v1/auth/refresh')
        .send({ refreshToken })
        .expect(200);

      expect(response.body).toHaveProperty('success', true);
      expect(response.body).toHaveProperty('data');
      expect(response.body.data).toHaveProperty('token');
      expect(response.body.data).toHaveProperty('refreshToken');
    });

    test('应该拒绝无效的刷新令牌', async () => {
      if (!app) {
        expect(true).toBe(false);
        return;
      }

      const response = await request(app)
        .post('/api/v1/auth/refresh')
        .send({ refreshToken: 'invalid_refresh_token' })
        .expect(401);

      expect(response.body).toHaveProperty('error');
      expect(response.body.error.message).toContain('无效的刷新令牌');
    });
  });
});
