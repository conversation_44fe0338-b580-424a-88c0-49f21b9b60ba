/**
 * 护理记录模型
 * 处理护理记录数据的CRUD操作
 */

const databaseManager = require('../config/database');
const Plant = require('./Plant');

class CareRecord {
  constructor(careData) {
    this.id = careData.id;
    this.plantId = careData.plant_id || careData.plantId;
    this.userId = careData.user_id || careData.userId;
    this.type = careData.type;
    this.description = careData.description;
    this.amount = careData.amount;
    this.notes = careData.notes;
    this.careDate = careData.care_date || careData.careDate;
    this.createdAt = careData.created_at || careData.createdAt;
    this.updatedAt = careData.updated_at || careData.updatedAt;
  }

  /**
   * 护理类型枚举
   */
  static get CARE_TYPES() {
    return [
      'watering',      // 浇水
      'fertilizing',   // 施肥
      'pruning',       // 修剪
      'repotting',     // 换盆
      'pest_control',  // 病虫害防治
      'cleaning',      // 清洁
      'observation',   // 观察记录
      'other'          // 其他
    ];
  }

  /**
   * 创建护理记录表（如果不存在）
   */
  static async createTable() {
    const pool = databaseManager.getPool();
    if (!pool) {
      console.log('数据库连接不可用，跳过表创建');
      return;
    }

    try {
      const createTableQuery = `
        CREATE TABLE IF NOT EXISTS care_records (
          id SERIAL PRIMARY KEY,
          plant_id INTEGER NOT NULL,
          user_id INTEGER NOT NULL,
          type VARCHAR(50) NOT NULL,
          description TEXT,
          amount VARCHAR(100),
          notes TEXT,
          care_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
          created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
          updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        );
        
        CREATE INDEX IF NOT EXISTS idx_care_records_plant_id ON care_records(plant_id);
        CREATE INDEX IF NOT EXISTS idx_care_records_user_id ON care_records(user_id);
        CREATE INDEX IF NOT EXISTS idx_care_records_type ON care_records(type);
        CREATE INDEX IF NOT EXISTS idx_care_records_care_date ON care_records(care_date);
      `;

      await pool.query(createTableQuery);
      console.log('✅ 护理记录表创建成功');
    } catch (error) {
      console.error('❌ 护理记录表创建失败:', error.message);
    }
  }

  /**
   * 验证护理类型
   */
  static isValidCareType(type) {
    return CareRecord.CARE_TYPES.includes(type);
  }

  /**
   * 创建新护理记录
   */
  static async create(careData, userId) {
    const pool = databaseManager.getPool();

    // 验证护理类型
    if (!CareRecord.isValidCareType(careData.type)) {
      throw new Error(`无效的护理类型: ${careData.type}`);
    }

    // 验证植物是否存在且属于用户
    const plant = await Plant.findByIdAndUserId(careData.plantId, userId);
    if (!plant) {
      throw new Error('植物不存在或不属于当前用户');
    }

    if (!pool) {
      // 测试环境模拟数据
      if (process.env.NODE_ENV === 'test') {
        const mockCareRecords = CareRecord.getMockCareRecords();
        const newCareRecord = {
          id: mockCareRecords.length + 1,
          plant_id: careData.plantId,
          user_id: userId,
          type: careData.type,
          description: careData.description,
          amount: careData.amount,
          notes: careData.notes,
          care_date: careData.careDate || new Date(),
          created_at: new Date(),
          updated_at: new Date()
        };
        mockCareRecords.push(newCareRecord);
        return new CareRecord(newCareRecord);
      }
      throw new Error('数据库连接不可用');
    }

    try {
      const query = `
        INSERT INTO care_records (plant_id, user_id, type, description, amount, notes, care_date)
        VALUES ($1, $2, $3, $4, $5, $6, $7)
        RETURNING id, plant_id, user_id, type, description, amount, notes, care_date, created_at, updated_at
      `;
      
      const result = await pool.query(query, [
        careData.plantId,
        userId,
        careData.type,
        careData.description,
        careData.amount,
        careData.notes,
        careData.careDate || new Date()
      ]);

      return new CareRecord(result.rows[0]);
    } catch (error) {
      console.error('创建护理记录失败:', error);
      throw error;
    }
  }

  /**
   * 根据用户ID获取护理记录列表
   */
  static async findByUserId(userId, options = {}) {
    const pool = databaseManager.getPool();
    
    if (!pool) {
      // 测试环境模拟数据
      if (process.env.NODE_ENV === 'test') {
        const mockCareRecords = CareRecord.getMockCareRecords();
        let userCareRecords = mockCareRecords.filter(record => record.user_id === parseInt(userId));
        
        // 应用筛选条件
        if (options.plantId) {
          userCareRecords = userCareRecords.filter(record => 
            record.plant_id === parseInt(options.plantId)
          );
        }
        
        if (options.type) {
          userCareRecords = userCareRecords.filter(record => 
            record.type === options.type
          );
        }
        
        if (options.startDate || options.endDate) {
          userCareRecords = userCareRecords.filter(record => {
            const careDate = new Date(record.care_date);
            if (options.startDate && careDate < new Date(options.startDate)) {
              return false;
            }
            if (options.endDate && careDate > new Date(options.endDate)) {
              return false;
            }
            return true;
          });
        }
        
        // 应用分页
        const page = options.page || 1;
        const limit = options.limit || 10;
        const offset = (page - 1) * limit;
        const paginatedRecords = userCareRecords.slice(offset, offset + limit);
        
        return {
          careRecords: paginatedRecords.map(record => new CareRecord(record)),
          total: userCareRecords.length,
          page: page,
          limit: limit,
          totalPages: Math.ceil(userCareRecords.length / limit)
        };
      }
      return { careRecords: [], total: 0, page: 1, limit: 10, totalPages: 0 };
    }

    try {
      let whereClause = 'WHERE user_id = $1';
      let queryParams = [userId];
      let paramIndex = 2;

      // 添加筛选条件
      if (options.plantId) {
        whereClause += ` AND plant_id = $${paramIndex}`;
        queryParams.push(options.plantId);
        paramIndex++;
      }

      if (options.type) {
        whereClause += ` AND type = $${paramIndex}`;
        queryParams.push(options.type);
        paramIndex++;
      }

      if (options.startDate) {
        whereClause += ` AND care_date >= $${paramIndex}`;
        queryParams.push(options.startDate);
        paramIndex++;
      }

      if (options.endDate) {
        whereClause += ` AND care_date <= $${paramIndex}`;
        queryParams.push(options.endDate);
        paramIndex++;
      }

      // 计算总数
      const countQuery = `SELECT COUNT(*) FROM care_records ${whereClause}`;
      const countResult = await pool.query(countQuery, queryParams);
      const total = parseInt(countResult.rows[0].count);

      // 应用分页
      const page = options.page || 1;
      const limit = options.limit || 10;
      const offset = (page - 1) * limit;

      const query = `
        SELECT id, plant_id, user_id, type, description, amount, notes, care_date, created_at, updated_at
        FROM care_records ${whereClause}
        ORDER BY care_date DESC
        LIMIT $${paramIndex} OFFSET $${paramIndex + 1}
      `;
      
      queryParams.push(limit, offset);
      const result = await pool.query(query, queryParams);

      return {
        careRecords: result.rows.map(row => new CareRecord(row)),
        total,
        page,
        limit,
        totalPages: Math.ceil(total / limit)
      };
    } catch (error) {
      console.error('获取护理记录列表失败:', error);
      throw error;
    }
  }

  /**
   * 根据ID和用户ID查找护理记录
   */
  static async findByIdAndUserId(id, userId) {
    const pool = databaseManager.getPool();
    
    if (!pool) {
      // 测试环境模拟数据
      if (process.env.NODE_ENV === 'test') {
        const mockCareRecords = CareRecord.getMockCareRecords();
        const recordData = mockCareRecords.find(record => 
          record.id === parseInt(id) && record.user_id === parseInt(userId)
        );
        return recordData ? new CareRecord(recordData) : null;
      }
      return null;
    }

    try {
      const query = 'SELECT * FROM care_records WHERE id = $1 AND user_id = $2';
      const result = await pool.query(query, [id, userId]);
      
      if (result.rows.length === 0) {
        return null;
      }

      return new CareRecord(result.rows[0]);
    } catch (error) {
      console.error('查找护理记录失败:', error);
      throw error;
    }
  }

  /**
   * 更新护理记录
   */
  static async updateByIdAndUserId(id, userId, updateData) {
    const pool = databaseManager.getPool();
    
    // 验证护理类型（如果提供）
    if (updateData.type && !CareRecord.isValidCareType(updateData.type)) {
      throw new Error(`无效的护理类型: ${updateData.type}`);
    }
    
    if (!pool) {
      // 测试环境模拟数据
      if (process.env.NODE_ENV === 'test') {
        const mockCareRecords = CareRecord.getMockCareRecords();
        const recordIndex = mockCareRecords.findIndex(record => 
          record.id === parseInt(id) && record.user_id === parseInt(userId)
        );
        
        if (recordIndex === -1) {
          return null;
        }
        
        // 更新护理记录数据
        Object.assign(mockCareRecords[recordIndex], updateData, { updated_at: new Date() });
        return new CareRecord(mockCareRecords[recordIndex]);
      }
      return null;
    }

    try {
      const setClause = [];
      const values = [];
      let paramIndex = 1;

      // 构建动态更新语句
      Object.keys(updateData).forEach(key => {
        if (updateData[key] !== undefined) {
          setClause.push(`${key} = $${paramIndex}`);
          values.push(updateData[key]);
          paramIndex++;
        }
      });

      if (setClause.length === 0) {
        throw new Error('没有提供更新数据');
      }

      setClause.push(`updated_at = $${paramIndex}`);
      values.push(new Date());
      values.push(id, userId);

      const query = `
        UPDATE care_records 
        SET ${setClause.join(', ')}
        WHERE id = $${paramIndex + 1} AND user_id = $${paramIndex + 2}
        RETURNING *
      `;

      const result = await pool.query(query, values);
      
      if (result.rows.length === 0) {
        return null;
      }

      return new CareRecord(result.rows[0]);
    } catch (error) {
      console.error('更新护理记录失败:', error);
      throw error;
    }
  }

  /**
   * 删除护理记录
   */
  static async deleteByIdAndUserId(id, userId) {
    const pool = databaseManager.getPool();
    
    if (!pool) {
      // 测试环境模拟数据
      if (process.env.NODE_ENV === 'test') {
        const mockCareRecords = CareRecord.getMockCareRecords();
        const recordIndex = mockCareRecords.findIndex(record => 
          record.id === parseInt(id) && record.user_id === parseInt(userId)
        );
        
        if (recordIndex === -1) {
          return false;
        }
        
        mockCareRecords.splice(recordIndex, 1);
        return true;
      }
      return false;
    }

    try {
      const query = 'DELETE FROM care_records WHERE id = $1 AND user_id = $2';
      const result = await pool.query(query, [id, userId]);
      
      return result.rowCount > 0;
    } catch (error) {
      console.error('删除护理记录失败:', error);
      throw error;
    }
  }

  /**
   * 转换为JSON对象
   */
  toJSON() {
    return {
      id: this.id,
      plantId: this.plantId,
      userId: this.userId,
      type: this.type,
      description: this.description,
      amount: this.amount,
      notes: this.notes,
      careDate: this.careDate,
      createdAt: this.createdAt,
      updatedAt: this.updatedAt
    };
  }

  /**
   * 获取模拟护理记录数据（用于测试）
   */
  static getMockCareRecords() {
    if (!CareRecord._mockCareRecords) {
      CareRecord._mockCareRecords = [];
    }
    return CareRecord._mockCareRecords;
  }

  /**
   * 清除模拟护理记录数据（用于测试）
   */
  static clearMockCareRecords() {
    CareRecord._mockCareRecords = [];
  }
}

module.exports = CareRecord;
