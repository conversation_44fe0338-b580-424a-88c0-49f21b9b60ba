/**
 * 用户模型
 * 处理用户数据的CRUD操作
 */

const bcrypt = require('bcryptjs');
const databaseManager = require('../config/database');

class User {
  constructor(userData) {
    this.id = userData.id;
    this.username = userData.username;
    this.email = userData.email;
    this.password = userData.password;
    this.created_at = userData.created_at;
    this.updated_at = userData.updated_at;
  }

  /**
   * 创建用户表（如果不存在）
   */
  static async createTable() {
    const pool = databaseManager.getPool();
    if (!pool) {
      console.log('数据库连接不可用，跳过表创建');
      return;
    }

    try {
      const createTableQuery = `
        CREATE TABLE IF NOT EXISTS users (
          id SERIAL PRIMARY KEY,
          username VARCHAR(50) UNIQUE NOT NULL,
          email VARCHAR(100) UNIQUE NOT NULL,
          password VARCHAR(255) NOT NULL,
          created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
          updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        );

        CREATE INDEX IF NOT EXISTS idx_users_email ON users(email);
        CREATE INDEX IF NOT EXISTS idx_users_username ON users(username);
      `;

      await pool.query(createTableQuery);
      console.log('✅ 用户表创建成功');
    } catch (error) {
      console.error('❌ 用户表创建失败:', error.message);
    }
  }

  /**
   * 根据邮箱查找用户
   */
  static async findByEmail(email) {
    const pool = databaseManager.getPool();
    if (!pool) {
      // 测试环境模拟数据
      if (process.env.NODE_ENV === 'test') {
        const mockUsers = User.getMockUsers();
        const userData = mockUsers.find(user => user.email === email);
        return userData ? new User(userData) : null;
      }
      return null;
    }

    try {
      const query = 'SELECT * FROM users WHERE email = $1';
      const result = await pool.query(query, [email]);

      if (result.rows.length === 0) {
        return null;
      }

      return new User(result.rows[0]);
    } catch (error) {
      console.error('查找用户失败:', error);
      throw error;
    }
  }

  /**
   * 根据ID查找用户
   */
  static async findById(id) {
    const pool = databaseManager.getPool();
    if (!pool) {
      // 测试环境模拟数据
      if (process.env.NODE_ENV === 'test') {
        const mockUsers = User.getMockUsers();
        const userData = mockUsers.find(user => user.id === parseInt(id));
        return userData ? new User(userData) : null;
      }
      return null;
    }

    try {
      const query = 'SELECT * FROM users WHERE id = $1';
      const result = await pool.query(query, [id]);

      if (result.rows.length === 0) {
        return null;
      }

      return new User(result.rows[0]);
    } catch (error) {
      console.error('查找用户失败:', error);
      throw error;
    }
  }

  /**
   * 创建新用户
   */
  static async create(userData) {
    const pool = databaseManager.getPool();

    // 加密密码
    const saltRounds = 10;
    const hashedPassword = await bcrypt.hash(userData.password, saltRounds);

    if (!pool) {
      // 测试环境模拟数据
      if (process.env.NODE_ENV === 'test') {
        const mockUsers = User.getMockUsers();
        const newUser = {
          id: mockUsers.length + 1,
          username: userData.username,
          email: userData.email,
          password: hashedPassword,
          created_at: new Date(),
          updated_at: new Date()
        };
        mockUsers.push(newUser);
        return new User(newUser);
      }
      throw new Error('数据库连接不可用');
    }

    try {
      const query = `
        INSERT INTO users (username, email, password)
        VALUES ($1, $2, $3)
        RETURNING id, username, email, created_at, updated_at
      `;

      const result = await pool.query(query, [
        userData.username,
        userData.email,
        hashedPassword
      ]);

      return new User(result.rows[0]);
    } catch (error) {
      if (error.code === '23505') { // 唯一约束违反
        if (error.constraint.includes('email')) {
          throw new Error('邮箱已存在');
        }
        if (error.constraint.includes('username')) {
          throw new Error('用户名已存在');
        }
      }
      console.error('创建用户失败:', error);
      throw error;
    }
  }

  /**
   * 验证密码
   */
  async validatePassword(password) {
    return await bcrypt.compare(password, this.password);
  }

  /**
   * 转换为安全的JSON对象（不包含密码）
   */
  toSafeJSON() {
    const { password, ...safeUser } = this;
    return safeUser;
  }

  /**
   * 获取模拟用户数据（用于测试）
   */
  static getMockUsers() {
    if (!User._mockUsers) {
      User._mockUsers = [];
    }
    return User._mockUsers;
  }

  /**
   * 清除模拟用户数据（用于测试）
   */
  static clearMockUsers() {
    User._mockUsers = [];
  }
}

module.exports = User;
