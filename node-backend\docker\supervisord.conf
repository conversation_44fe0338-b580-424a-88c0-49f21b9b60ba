[supervisord]
nodaemon=true
logfile=/var/log/supervisor/supervisord.log
pidfile=/var/run/supervisord.pid
user=root

[program:postgresql]
command=/etc/init.d/postgresql start && tail -f /var/log/postgresql/postgresql-14-main.log
user=root
autostart=true
autorestart=true
stderr_logfile=/var/log/supervisor/postgresql.err.log
stdout_logfile=/var/log/supervisor/postgresql.out.log
priority=100
startsecs=5

[program:redis]
command=/usr/bin/redis-server /etc/redis/redis.conf
user=redis
autostart=true
autorestart=true
stderr_logfile=/var/log/supervisor/redis.err.log
stdout_logfile=/var/log/supervisor/redis.out.log
priority=200

[program:node-backend]
command=/usr/bin/node /app/src/app.js
directory=/app
user=root
autostart=true
autorestart=true
stderr_logfile=/var/log/supervisor/node-backend.err.log
stdout_logfile=/var/log/supervisor/node-backend.out.log
priority=300
environment=NODE_ENV=production
