#!/bin/bash

# PlantApp Backend Docker Build and Run Script

echo "🚀 Building PlantApp Backend Docker Image..."

# Navigate to the backend directory
cd "$(dirname "$0")"

# Build the Docker image
docker build -t plantapp-backend:latest .

if [ $? -eq 0 ]; then
    echo "✅ Docker image built successfully!"
    echo ""
    echo "To run the container, use one of these options:"
    echo ""
    echo "1. Using Docker Compose (recommended):"
    echo "   docker-compose up -d"
    echo ""
    echo "2. Using Docker run:"
    echo "   docker run -d --name plantapp-backend -p 3000:3000 -p 5432:5432 -p 6379:6379 plantapp-backend:latest"
    echo ""
    echo "3. Access the application:"
    echo "   - Backend API: http://localhost:3000"
    echo "   - Health Check: http://localhost:3000/health"
    echo "   - PostgreSQL: localhost:5432"
    echo "   - Redis: localhost:6379"
    echo ""
else
    echo "❌ Docker build failed!"
    exit 1
fi
