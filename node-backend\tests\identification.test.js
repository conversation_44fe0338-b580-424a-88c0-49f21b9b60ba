/**
 * 植物识别API测试
 */

const request = require('supertest');
const app = require('../src/app');

describe('Plant Identification API', () => {
  describe('POST /api/v1/identification/identify', () => {
    it('should return validation error when no input provided', async () => {
      const response = await request(app)
        .post('/api/v1/identification/identify')
        .send({});

      expect(response.status).toBe(400);
      expect(response.body.success).toBe(false);
      expect(response.body.message).toContain('At least one input type');
    });

    it('should accept text description input', async () => {
      const response = await request(app)
        .post('/api/v1/identification/identify')
        .send({
          textDescription: 'A green plant with large leaves and white flowers'
        });

      // 如果没有配置OpenRouter API key，应该返回错误
      // 如果配置了，应该返回识别结果
      expect(response.status).toBeOneOf([200, 500]);
    });

    it('should validate text description length', async () => {
      const longText = 'a'.repeat(2001);
      const response = await request(app)
        .post('/api/v1/identification/identify')
        .send({
          textDescription: longText
        });

      expect(response.status).toBe(400);
      expect(response.body.success).toBe(false);
    });

    it('should accept molecular data input', async () => {
      const response = await request(app)
        .post('/api/v1/identification/identify')
        .send({
          molecularData: 'ATCGATCGATCGATCG'
        });

      expect(response.status).toBeOneOf([200, 500]);
    });

    it('should accept base64 image input', async () => {
      const response = await request(app)
        .post('/api/v1/identification/identify')
        .send({
          imageBase64: 'iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg=='
        });

      expect(response.status).toBeOneOf([200, 500]);
    });

    it('should accept multiple input types', async () => {
      const response = await request(app)
        .post('/api/v1/identification/identify')
        .send({
          textDescription: 'A green plant with large leaves',
          molecularData: 'ATCGATCGATCGATCG',
          imageBase64: 'iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg=='
        });

      expect(response.status).toBeOneOf([200, 500]);
    });

    it('should accept options parameter', async () => {
      const response = await request(app)
        .post('/api/v1/identification/identify')
        .send({
          textDescription: 'A green plant with large leaves',
          options: {
            model: 'anthropic/claude-3-haiku',
            temperature: 0.5
          }
        });

      expect(response.status).toBeOneOf([200, 500]);
    });

    it('should validate temperature range', async () => {
      const response = await request(app)
        .post('/api/v1/identification/identify')
        .send({
          textDescription: 'A green plant',
          options: {
            temperature: 3.0 // Invalid temperature
          }
        });

      expect(response.status).toBe(400);
      expect(response.body.success).toBe(false);
    });
  });

  describe('POST /api/v1/identification/care-advice', () => {
    it('should return validation error when plant name is missing', async () => {
      const response = await request(app)
        .post('/api/v1/identification/care-advice')
        .send({});

      expect(response.status).toBe(400);
      expect(response.body.success).toBe(false);
    });

    it('should accept plant name', async () => {
      const response = await request(app)
        .post('/api/v1/identification/care-advice')
        .send({
          plantName: 'Monstera deliciosa'
        });

      expect(response.status).toBeOneOf([200, 500]);
    });

    it('should accept conditions parameter', async () => {
      const response = await request(app)
        .post('/api/v1/identification/care-advice')
        .send({
          plantName: 'Monstera deliciosa',
          conditions: {
            location: 'Indoor',
            climate: 'Tropical',
            season: 'summer'
          }
        });

      expect(response.status).toBeOneOf([200, 500]);
    });

    it('should validate season values', async () => {
      const response = await request(app)
        .post('/api/v1/identification/care-advice')
        .send({
          plantName: 'Monstera deliciosa',
          conditions: {
            season: 'invalid-season'
          }
        });

      expect(response.status).toBe(400);
      expect(response.body.success).toBe(false);
    });

    it('should validate plant name length', async () => {
      const longName = 'a'.repeat(201);
      const response = await request(app)
        .post('/api/v1/identification/care-advice')
        .send({
          plantName: longName
        });

      expect(response.status).toBe(400);
      expect(response.body.success).toBe(false);
    });
  });

  describe('GET /api/v1/identification/service-status', () => {
    it('should return service status', async () => {
      const response = await request(app)
        .get('/api/v1/identification/service-status');

      expect(response.status).toBeOneOf([200, 503]);
      expect(response.body).toHaveProperty('data');
      expect(response.body.data).toHaveProperty('status');
      expect(response.body.data).toHaveProperty('timestamp');
    });
  });

  describe('File Upload', () => {
    it('should accept image file upload', async () => {
      // 创建一个简单的1x1像素PNG图片的Buffer
      const imageBuffer = Buffer.from([
        0x89, 0x50, 0x4E, 0x47, 0x0D, 0x0A, 0x1A, 0x0A,
        0x00, 0x00, 0x00, 0x0D, 0x49, 0x48, 0x44, 0x52,
        0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x01,
        0x08, 0x06, 0x00, 0x00, 0x00, 0x1F, 0x15, 0xC4,
        0x89, 0x00, 0x00, 0x00, 0x0D, 0x49, 0x44, 0x41,
        0x54, 0x78, 0xDA, 0x63, 0x60, 0x60, 0x60, 0x00,
        0x00, 0x00, 0x04, 0x00, 0x01, 0x27, 0x6B, 0xB6,
        0x47, 0x00, 0x00, 0x00, 0x00, 0x49, 0x45, 0x4E,
        0x44, 0xAE, 0x42, 0x60, 0x82
      ]);

      const response = await request(app)
        .post('/api/v1/identification/identify')
        .attach('image', imageBuffer, 'test.png');

      expect(response.status).toBeOneOf([200, 400, 500]);
    });

    it('should reject non-image files', async () => {
      const textBuffer = Buffer.from('This is not an image');

      const response = await request(app)
        .post('/api/v1/identification/identify')
        .attach('image', textBuffer, 'test.txt');

      expect(response.status).toBe(400);
      expect(response.body.success).toBe(false);
      expect(response.body.message).toContain('image files');
    });
  });
});

// 自定义匹配器
expect.extend({
  toBeOneOf(received, expected) {
    const pass = expected.includes(received);
    if (pass) {
      return {
        message: () => `expected ${received} not to be one of ${expected}`,
        pass: true,
      };
    } else {
      return {
        message: () => `expected ${received} to be one of ${expected}`,
        pass: false,
      };
    }
  },
});
