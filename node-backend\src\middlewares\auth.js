/**
 * 认证中间件
 * 处理JWT令牌验证和用户认证
 */

const JWTUtils = require('../utils/jwt');
const User = require('../models/User');

/**
 * JWT认证中间件
 */
const authenticateToken = async (req, res, next) => {
  try {
    const authHeader = req.headers.authorization;
    
    if (!authHeader) {
      return res.status(401).json({
        error: {
          message: '未提供认证令牌',
          status: 401
        }
      });
    }

    const token = JWTUtils.extractTokenFromHeader(authHeader);
    const decoded = JWTUtils.verifyAccessToken(token);

    // 获取用户信息
    const user = await User.findById(decoded.userId);
    if (!user) {
      return res.status(401).json({
        error: {
          message: '用户不存在',
          status: 401
        }
      });
    }

    // 将用户信息添加到请求对象
    req.user = user;
    next();
  } catch (error) {
    return res.status(401).json({
      error: {
        message: error.message || '无效的认证令牌',
        status: 401
      }
    });
  }
};

/**
 * 可选认证中间件（不强制要求认证）
 */
const optionalAuth = async (req, res, next) => {
  try {
    const authHeader = req.headers.authorization;
    
    if (authHeader) {
      const token = JWTUtils.extractTokenFromHeader(authHeader);
      const decoded = JWTUtils.verifyAccessToken(token);
      const user = await User.findById(decoded.userId);
      
      if (user) {
        req.user = user;
      }
    }
    
    next();
  } catch (error) {
    // 可选认证失败时不返回错误，继续执行
    next();
  }
};

/**
 * 权限检查中间件
 */
const requirePermission = (permission) => {
  return (req, res, next) => {
    if (!req.user) {
      return res.status(401).json({
        error: {
          message: '需要认证',
          status: 401
        }
      });
    }

    // 这里可以扩展权限检查逻辑
    // 目前所有认证用户都有基本权限
    next();
  };
};

module.exports = {
  authenticateToken,
  optionalAuth,
  requirePermission
};
